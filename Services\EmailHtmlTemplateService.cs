﻿using Comman.Helper.Extensions;
using Comman.Services.Interfaces;
using MimeKit;
using System.Globalization;
using System.Text;
using TaskDotNet.Localization;
using TaskDotNet.Models;
using System.Resources;
using Microsoft.Extensions.Caching.Memory;

namespace TaskDotNet.Services
{
    public class EmailHtmlTemplateService : IEmailHtmlTemplateService
    {
        #region Fields and Constructor
        private readonly IConfiguration _configuration;
        private readonly IWebHostEnvironment _environment;
        private readonly IMemoryCache _memoryCache;
        private readonly string _activitiesLink;
        private readonly string _headerImage;
        private static readonly ResourceManager _sharedResourceManager = new(typeof(SharedResource));

        public EmailHtmlTemplateService(
            IConfiguration configuration,
            IWebHostEnvironment environment,
            IMemoryCache memoryCache)
        {
            _configuration = configuration;
            _environment = environment;
            _memoryCache = memoryCache;
            _activitiesLink = _configuration["ProjectsLinks:ActivitesLink"];
            _headerImage = _configuration.GetSection("EmailImages")["companyLogo"];
        }
        #endregion

        #region Main Template Method
        public async Task<string> GetActivityEmailTemplate(string userCompanyName, Activity activity, string lang)
        {
            return activity.ActivityType switch
            {
                Helper.ActivityType.Moving => await GetMovingTemplate(userCompanyName, activity, lang),
                Helper.ActivityType.Cleaning => await GetCleaningTemplate(userCompanyName, activity, lang),
                Helper.ActivityType.MovingAndCleaning => await GetMovingAndCleaningTemplate(userCompanyName, activity, lang),
                Helper.ActivityType.PaintingAndGisper => await GetPaintingTemplate(userCompanyName, activity, lang),
                _ => await GetWorkersTemplate(userCompanyName, activity, lang)
            };
        }
        #endregion

        #region Template Methods
        private async Task<string> GetMovingTemplate(string userCompanyName, Activity activity, string lang)
        {
            var template = await GetTemplateContent("RequestFrontendMoving", lang);
            var inventoryHtml = GetInventoryHtml(activity.InventoryItems, lang);
            var templateData = CreateMovingTemplateData(userCompanyName, activity, inventoryHtml, lang);

            return string.Format(template, templateData);
        }

        private async Task<string> GetMovingAndCleaningTemplate(string userCompanyName, Activity activity, string lang)
        {
            var template = await GetTemplateContent("RequestFrontendMovingAndCleaning", lang);
            var inventoryHtml = GetInventoryHtml(activity.InventoryItems, lang);
            var templateData = CreateMovingAndCleaningTemplateData(userCompanyName, activity, inventoryHtml, lang);

            return string.Format(template, templateData);
        }

        private async Task<string> GetWorkersTemplate(string userCompanyName, Activity activity, string lang)
        {
            var template = await GetTemplateContent("RequestFrontendWorkers", lang);
            var templateData = CreateWorkersTemplateData(userCompanyName, activity, lang);

            return string.Format(template, templateData);
        }

        private async Task<string> GetPaintingTemplate(string userCompanyName, Activity activity, string lang)
        {
            var template = await GetTemplateContent("RequestFrontendPainting", lang);
            var templateData = CreatePaintingTemplateData(userCompanyName, activity, lang);

            return string.Format(template, templateData);
        }

        private async Task<string> GetCleaningTemplate(string userCompanyName, Activity activity, string lang)
        {
            var template = await GetTemplateContent("RequestFrontendCleaning", lang);
            var templateData = CreateCleaningTemplateData(userCompanyName, activity, lang);

            return string.Format(template, templateData);
        }
        #endregion

        #region Template Data Creation
        private object[] CreateMovingTemplateData(string userCompanyName, Activity activity, string inventoryHtml, string lang)
        {
            var commonData = CreateCommonTemplateData(userCompanyName, activity, lang);
            var movingSpecific = new object[]
            {
                activity.Room,
                activity.Object,
                activity.Area,
                activity.Floor,
                GetYesNoText(activity.FLift),
                GetDistanceText(activity.FDistance, SharedResource.toTheLoadingEdge),
                activity.TObject,
                activity.TFloor,
                GetYesNoText(activity.TLift),
                GetDistanceText(activity.TDistance, SharedResource.toTheDrainEdge),
                GetYesNoText(activity.FDismantFurnit),
                GetYesNoText(activity.FDismantLamp),
                GetYesNoText(activity.FWrapUp),
                GetYesNoText(activity.Mobellift),
                GetYesNoText(activity.Klavier),
                GetYesNoText(activity.Schwer),
                GetYesNoText(activity.Celler),
                GetYesNoText(activity.Garage),
                GetYesNoText(activity.TMountMöbel),
                GetYesNoText(activity.TmontLamp),
                GetYesNoText(activity.TAuspacken),
                GetYesNoText(activity.Lager),
                GetYesNoText(activity.Disposal),
                GetKartonsText(activity.Kartons),
                activity.Focus,
                GetViewingDateText(activity.ViewingDate),
                inventoryHtml
            };

            return CombineArrays(commonData, movingSpecific);
        }

        private object[] CreateMovingAndCleaningTemplateData(string userCompanyName, Activity activity, string inventoryHtml, string lang)
        {
            var movingData = CreateMovingTemplateData(userCompanyName, activity, inventoryHtml, lang);
            var cleaningSpecific = new object[]
            {
                activity.Room,
                activity.Object,
                activity.Floor,
                FormatDate(activity.CleaningDate),
                FormatDate(activity.HandOverDate)
            };

            return CombineArrays(movingData, cleaningSpecific);
        }

        private object[] CreateWorkersTemplateData(string userCompanyName, Activity activity, string lang)
        {
            return new object[]
            {
                activity.OrderNr,
                userCompanyName,
                activity.ActivityType.GetDisplayName(),
                FormatDate(activity.MovingDate),
                GetPlaceholder(), GetPlaceholder(), GetPlaceholder(), GetPlaceholder(),
                $"{activity.PostBox} {activity.City}",
                activity.Object,
                activity.Flexible,
                activity.Focus,
                _activitiesLink,
                _headerImage,
                GetNotesHtml(activity.Notes)
            };
        }

        private object[] CreatePaintingTemplateData(string userCompanyName, Activity activity, string lang)
        {
            var commonData = CreateCommonTemplateData(userCompanyName, activity, lang);
            var paintingSpecific = new object[]
            {
                activity.Room,
                activity.Object,
                activity.Area,
                activity.Floor,
                GetYesNoText(activity.Washroom),
                activity.Flexible,
                activity.Focus,
                GetViewingDateText(activity.ViewingDate),
                activity.Walls,
                activity.Doors,
                activity.Windows
            };

            return CombineArrays(commonData, paintingSpecific);
        }

        private object[] CreateCleaningTemplateData(string userCompanyName, Activity activity, string lang)
        {
            return new object[]
            {
                activity.OrderNr,
                userCompanyName,
                _headerImage,
                FormatDate(activity.CleaningDate),
                FormatDate(activity.HandOverDate),
                GetPlaceholder(), GetPlaceholder(), GetPlaceholder(), GetPlaceholder(),
                $"{activity.PostBox} {activity.City}",
                activity.Room,
                activity.Object,
                activity.Area,
                activity.Floor,
                activity.CleaningType,
                activity.SoilType,
                GetYesNoText(activity.CarpetCleaning),
                GetYesNoText(activity.HighPressure),
                GetYesNoText(activity.Balcony),
                GetYesNoText(activity.Shutters),
                GetYesNoText(activity.Washroom),
                activity.Flexible,
                activity.Focus,
                GetYesNoText(activity.VenetianBlinds),
                activity.BathroomToilet,
                activity.ShowerToilet,
                activity.Heater,
                activity.Doors,
                activity.Windows,
                _activitiesLink,
                activity.Toilets,
                GetNotesHtml(activity.Notes)
            };
        }

        private object[] CreateCommonTemplateData(string userCompanyName, Activity activity, string lang)
        {
            return new object[]
            {
                activity.OrderNr,
                userCompanyName,
                FormatDate(activity.MovingDate),
                GetPlaceholder(), GetPlaceholder(), GetPlaceholder(), GetPlaceholder(),
                $"{activity.PostBox} {activity.City}",
                GetPlaceholder(),
                $"{activity.TPostBox} {activity.TCity}",
                _activitiesLink,
                _headerImage,
                GetNotesHtml(activity.Notes)
            };
        }
        #endregion

        #region Helper Methods
        private async Task<string> GetTemplateContent(string templateName, string lang)
        {
            var cacheKey = $"template_{templateName}_{lang}";

            if (_memoryCache.TryGetValue(cacheKey, out string cachedTemplate))
                return cachedTemplate;

            var pathToFile = Path.Combine(_environment.WebRootPath, "Templates", $"{templateName}-{lang}.html");
            var template = await File.ReadAllTextAsync(pathToFile);

            _memoryCache.Set(cacheKey, template, TimeSpan.FromHours(1));
            return template;
        }

        private static string GetYesNoText(bool? value) =>
            value == true ? SharedResource.Yes : SharedResource.No;

        private static string GetDistanceText(int distance, string unit) =>
            distance > 0 ? $"{distance} m {unit}" : string.Empty;

        private static string GetKartonsText(int kartons) =>
            kartons > 0 ? $"{kartons} {SharedResource.Boxes} (30kg)" : string.Empty;

        private static string GetViewingDateText(DateTime viewingDate) =>
            viewingDate != DateTime.MinValue
                ? $"{SharedResource.YesOn} {FormatDate(viewingDate)}"
                : SharedResource.NotRequired;

        private static string GetNotesHtml(string notes) =>
            !string.IsNullOrEmpty(notes)
                ? $"<p style=\"color: darkblue\"><span style=\"font-weight:bold\">Notes: </span>{notes}</p>"
                : string.Empty;

        private static string FormatDate(DateTime date) => date.ToString("dd.MM.yyyy");

        private static string GetPlaceholder() => "xxxxxxxxxxxxxxxxxxxxxxxxxxx";

        private static object[] CombineArrays(object[] array1, object[] array2)
        {
            var result = new object[array1.Length + array2.Length];
            Array.Copy(array1, 0, result, 0, array1.Length);
            Array.Copy(array2, 0, result, array1.Length, array2.Length);
            return result;
        }
        #endregion

        #region Inventory HTML Generation
        private string GetInventoryHtml(List<ActivityInventoryItem> inventoryData, string lang)
        {
            if (!inventoryData?.Any() == true)
                return string.Empty;

            var html = new StringBuilder();
            BuildInventoryTable(html, inventoryData, lang);
            return html.ToString();
        }

        private void BuildInventoryTable(StringBuilder html, List<ActivityInventoryItem> inventoryData, string lang)
        {
            html.AppendLine($@"
                <div class='col'>
                    <h4 style='font-size:20px;font-weight: 600;text-align: center;text-decoration: underline;'>
                        {GetLocalizedValue("Inventory list", lang)}:
                    </h4>
                    <table class='table table-bordered table-responsive table-striped' style='border-collapse: collapse;width:100%;'>
                        <thead style='background-color: #008284; color:white; font-weight: bolder;'>
                            <tr>
                                <th style='padding: 8px; text-align: left; font-weight: bold;color: white;'>{GetLocalizedValue("Space", lang)}</th>
                                <th style='padding: 8px; text-align: left; font-weight: bold;color: white;'>{GetLocalizedValue("Items", lang)}</th>
                                <th style='padding: 8px; text-align: left; font-weight: bold;color: white;'>{GetLocalizedValue("Number", lang)}</th>
                                <th style='padding: 8px; text-align: left; font-weight: bold;color: white;'>{GetLocalizedValue("Total volume", lang)}</th>
                            </tr>
                        </thead>
                        <tbody>");

            var totalVolume = BuildInventoryRows(html, inventoryData, lang);
            BuildTotalRow(html, totalVolume, lang);

            html.AppendLine(@"
                        </tbody>
                    </table>
                </div>");
        }

        private decimal BuildInventoryRows(StringBuilder html, List<ActivityInventoryItem> inventoryData, string lang)
        {
            decimal totalVolume = 0;
            var groupedInventory = inventoryData
                .Where(item => !string.IsNullOrEmpty(item.Category))
                .GroupBy(item => item.Category)
                .ToList();

            foreach (var room in groupedInventory)
            {
                var furnitureList = room.ToList();

                foreach (var furniture in furnitureList)
                {
                    var furnitureName = GetLocalizedFurnitureName(furniture, lang);
                    var furnitureVolume = furniture.Total ?? 0;

                    html.AppendLine("<tr>");

                    if (furniture == furnitureList.First())
                    {
                        html.AppendLine($"<td rowspan='{furnitureList.Count}' style='border: 1px solid #ddd; padding: 8px;'>{room.Key}</td>");
                    }

                    html.AppendLine($@"
                        <td style='border: 1px solid #ddd; padding: 8px;'>{furnitureName}</td>
                        <td style='border: 1px solid #ddd; padding: 8px;'>{furniture.Count} {furniture.Unit}</td>
                        <td style='border: 1px solid #ddd; padding: 8px;'>{furnitureVolume:0.00} m3</td>");

                    html.AppendLine("</tr>");
                    totalVolume += (decimal)furnitureVolume;
                }
            }

            return totalVolume;
        }

        private void BuildTotalRow(StringBuilder html, decimal totalVolume, string lang)
        {
            html.AppendLine($@"
                <tr><td colspan='4' style='border: 1px solid #ddd; padding: 8px;'></td></tr>
                <tr class='total-weight-row' style='font-weight: bold;'>
                    <td colspan='3' style='text-align: right; border: 1px solid #ddd; padding: 8px;'>
                        {GetLocalizedValue("Total volume", lang)}
                    </td>
                    <td style='text-align: center; border: 1px solid #ddd; padding: 8px;'>
                        {totalVolume:0.00} m3
                    </td>
                </tr>");
        }

        private static string GetLocalizedFurnitureName(ActivityInventoryItem furniture, string lang) =>
            lang switch
            {
                "de" => furniture.GermanName,
                "it" => furniture.ItalianName,
                "fr" => furniture.FrenchName,
                _ => furniture.EnglishName
            };
        #endregion

        #region Localization
        public string GetLocalizedValue(string key, string cultureName)
        {
            try
            {
                return _sharedResourceManager.GetString(key, new CultureInfo(cultureName)) ?? key;
            }
            catch
            {
                return key;
            }
        }
        #endregion
    }
}