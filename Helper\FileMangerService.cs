﻿namespace Admin.TaskDotNet.Helper
{
    public class FileManagerService : IFileManagerService
    {
        private readonly IWebHostEnvironment _environment;
        private readonly string _nullImage = "/Images/null.png";
        public FileManagerService(IWebHostEnvironment environment)
        {
            _environment = environment;
        }

        public string UploadFile(IFormFile file, string folder)
        {
            if (file == null || file.Length <= 0)
            {
                return _nullImage;
            }

            var fileName = $"{file.FileName}";

            // Combine the folder path and the unique filename
            string imagePath = Path.Combine(_environment.WebRootPath, "Images", folder, fileName);

            // Ensure the target directory exists
            Directory.CreateDirectory(Path.GetDirectoryName(imagePath));

            // Save the file to the target location
            using (var stream = new FileStream(imagePath, FileMode.Create))
            {
                file.CopyTo(stream);
            }

            // Return the relative URL to the saved file
            string relativeUrl = Path.Combine("Images", folder, fileName).Replace("\\", "/");

            return "/" + relativeUrl;
        }

        public void DeleteFile(string relativePath)
        {
            if (string.IsNullOrWhiteSpace(relativePath))
            {
                throw new ArgumentException("Invalid file path.");
            }

            if(relativePath == _nullImage)
            {
                return;
            }  

            // Map the relative path to the absolute file path
            string absolutePath = Path.Combine(_environment.WebRootPath, relativePath.TrimStart('/').Replace("/", "\\"));

            if (File.Exists(absolutePath))
            {
                File.Delete(absolutePath);
            }
        }
    }
}

