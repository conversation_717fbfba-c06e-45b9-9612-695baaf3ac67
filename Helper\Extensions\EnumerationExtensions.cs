﻿using Comman.Helper.Extensions;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.ComponentModel.DataAnnotations;
using System.Reflection;

namespace Admin.TaskDotNet.Helper.Extensions
{
    public static class EnumerationExtensions
    {
        public static IEnumerable<SelectListItem> GetEnumSelectListAsString<TEnum>(this IHtmlHelper htmlHelper)
            where TEnum : struct, Enum
        {
            var enumType = typeof(TEnum);

            if (!enumType.IsEnum)
            {
                throw new ArgumentException("TEnum must be an enumeration type.");
            }

            var values = Enum.GetValues(enumType)
                             .Cast<TEnum>()
                             .Select(e => new SelectListItem
                             {
                                 Text = e.GetDisplayName(),
                                 Value = e.GetDisplayName()
                             });

            return values;
        }
    }
}
