﻿using Admin.TaskDotNet.Dtos;
using Comman.Services.Interfaces;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using MigraDoc.DocumentObjectModel;
using MigraDoc.Rendering;
using TaskDotNet.Helper;
using TaskDotNet.Models;


namespace Admin.TaskDotNet.Controllers
{

    public class StatisticsController : Controller
    {
        #region ctor

        private readonly IMapper mapper;
        private readonly IMovementService movementService;
        private readonly SignInManager<Partner> signInManager;
        private readonly UserManager<Partner> userManager;

        public StatisticsController(IMapper mapper, IMovementService movementService, SignInManager<Partner> signInManager, UserManager<Partner> _UserManager)
        {
            this.mapper = mapper;
            this.movementService = movementService;
            this.signInManager = signInManager;
            userManager = _UserManager;
        }
        #endregion

        #region Get all statics to Admin
        [Authorize(Roles = "Admin")]

        public async Task<IActionResult> AdminStatistics()
        {
            var (dataCountsByCategoryAndYearMonth, eachActivityCountAndSum) = movementService.GetDataAdminCountsByCategoryNameAndYearMonth();

            ViewBag.EachActivityCountAndSum = eachActivityCountAndSum;

            return View(dataCountsByCategoryAndYearMonth);

        }



        #endregion

        public async Task<IActionResult> GeneratePdf(StatisticsReportDto request)
        {

            var data = await movementService.GetMovementsAsync(request.AllPartners, request.CompanyName, request.PeriodFrom, request.PeriodTo);
            if (request.AllPartners == true)
            {
                var pdfAllContent = GeneratePdfAllContent(data, request);

                return File(pdfAllContent, "application/pdf", "statistics.pdf");
            }
            else if (request.AllPartners == false)
            {
                var pdfContent = GeneratePdfContent(data, request);

                return File(pdfContent, "application/pdf", "statistics.pdf");
            }
            return View();

        }



        public byte[] GeneratePdfContent(IEnumerable<Movement> data, StatisticsReportDto reportDto)
        {
            var document = new Document();
            var section = document.AddSection();

            // Add title and other static information
            var paragraph = section.AddParagraph("Lightsoft GmbH\nBahnhofstrasse 34\n8005 Zürich\n\n");
            paragraph.Format.Font.Size = 14;
            paragraph.Format.Font.Bold = true;

            string dateRange = $"{reportDto.PeriodFrom:dd.MM.yyyy} – {reportDto.PeriodTo:dd.MM.yyyy}";
            paragraph = section.AddParagraph($"Statistikliste\nPartner: {reportDto.CompanyName ?? "All Partners"}\n{dateRange}\n\n");
            paragraph.Format.Font.Size = 12;
            paragraph.Format.Font.Bold = true;

            // Create table
            var table = section.AddTable();
            table.Borders.Width = 0.75;

            // Define columns with widths
            table.AddColumn(Unit.FromCentimeter(2));  // Datum
            table.AddColumn(Unit.FromCentimeter(4));  // Order-Nr.
            table.AddColumn(Unit.FromCentimeter(4));  // Aktivität
            table.AddColumn(Unit.FromCentimeter(2));  // PLZ
            table.AddColumn(Unit.FromCentimeter(2));  // City
            table.AddColumn(Unit.FromCentimeter(2));  // Preis

            // Create header row
            var row = table.AddRow();
            row.Format.Font.Bold = true;
            row.Shading.Color = Colors.LightGray;
            row.Cells[0].AddParagraph("Datum");
            row.Cells[1].AddParagraph("Order-Nr.");
            row.Cells[2].AddParagraph("Aktivität");
            row.Cells[3].AddParagraph("PLZ");
            row.Cells[4].AddParagraph("City");
            row.Cells[5].AddParagraph("Preis");

            decimal totalPrice = 0;

            // Add data rows
            foreach (var item in data)
            {
                if (item.Activity != null)
                {
                    switch (item.Activity.ActivityType)
                    {
                        case ActivityType.Moving:
                            item.Activity.Name = "Umzug";
                            break;
                        case ActivityType.Cleaning:
                            item.Activity.Name = "Reinigung";
                            break;
                        case ActivityType.MovingAndCleaning:
                            item.Activity.Name = "Umzug & Reinigung";
                            break;
                        case ActivityType.PaintingAndGisper:
                            item.Activity.Name = "Painting";
                            break;
                        case ActivityType.Electrician:
                            item.Activity.Name = "Gisper";
                            break;
                        default:
                            break;
                    }
                }

                row = table.AddRow();
                row.Cells[0].AddParagraph(item.Purchase_date.ToString("dd.MM.yyyy"));
                row.Cells[1].AddParagraph(item.Order_Nr ?? "N/A");
                row.Cells[2].AddParagraph(item.Activity?.Name ?? "N/A");
                row.Cells[3].AddParagraph("8004"); // Example PLZ
                row.Cells[4].AddParagraph("Zürich"); // Example City
                row.Cells[5].AddParagraph(item.Order_Price.ToString("F2"));

                totalPrice += item.Order_Price;
            }

            // Add total price row
            row = table.AddRow();
            row.Cells[0].MergeRight = 4;
            row.Cells[0].AddParagraph("Total");
            row.Cells[5].AddParagraph(totalPrice.ToString("F2"));

            // Render document to PDF
            var pdfRenderer = new PdfDocumentRenderer(true) { Document = document };
            pdfRenderer.RenderDocument();

            using (var ms = new MemoryStream())
            {
                pdfRenderer.PdfDocument.Save(ms);
                return ms.ToArray();
            }
        }

        public byte[] GeneratePdfAllContent(IEnumerable<Movement> data, StatisticsReportDto reportDto)
        {
            var document = new Document();
            var section = document.AddSection();

            // Add title and other static information
            var paragraph = section.AddParagraph("Lightsoft GmbH\nBahnhofstrasse 34\n8005 Zürich\n\n");
            paragraph.Format.Font.Size = 14;
            paragraph.Format.Font.Bold = true;

            string dateRange = $"{reportDto.PeriodFrom:dd.MM.yyyy} – {reportDto.PeriodTo:dd.MM.yyyy}";
            paragraph = section.AddParagraph($"Statistikliste\n{(reportDto.AllPartners ? "Alle Partner" : $"Partner: {reportDto.CompanyName}")}\n{dateRange}\n\n");
            paragraph.Format.Font.Size = 12;
            paragraph.Format.Font.Bold = true;

            // Create table
            var table = section.AddTable();
            table.Borders.Width = 0.75;

            // Define columns with widths
            table.AddColumn(Unit.FromCentimeter(6));  // Firma
            table.AddColumn(Unit.FromCentimeter(4));  // Order-Nr.
            table.AddColumn(Unit.FromCentimeter(4));  // Aktivität
            table.AddColumn(Unit.FromCentimeter(2));  // Preis

            // Create header row
            var row = table.AddRow();
            row.Format.Font.Bold = true;
            row.Shading.Color = Colors.LightGray;
            row.Cells[0].AddParagraph("Firma");
            row.Cells[1].AddParagraph("Order-Nr.");
            row.Cells[2].AddParagraph("Aktivität");
            row.Cells[3].AddParagraph("Preis");

            decimal totalPrice = 0;

            // Add data rows
            foreach (var item in data)
            {
                if (item.Activity != null)
                {
                    // Correct activity names based on Categoryid
                    switch (item.Activity.ActivityType)
                    {
                        case ActivityType.Moving:
                            item.Activity.Name = "Umzug";
                            break;
                        case ActivityType.Cleaning:
                            item.Activity.Name = "Reinigung";
                            break;
                        case ActivityType.MovingAndCleaning:
                            item.Activity.Name = "Umzug & Reinigung";
                            break;
                        case ActivityType.PaintingAndGisper:
                            item.Activity.Name = "Painting";
                            break;
                        case ActivityType.Electrician:
                            item.Activity.Name = "Gisper";
                            break;
                        default:
                            break;
                    }
                }

                row = table.AddRow();
                row.Cells[0].AddParagraph(item.Partner_Name ?? "N/A");
                row.Cells[1].AddParagraph(item.Order_Nr ?? "N/A");
                row.Cells[2].AddParagraph(item.Activity?.Name ?? "N/A");
                row.Cells[3].AddParagraph(item.Order_Price.ToString("F2"));

                totalPrice += item.Order_Price;
            }

            // Add total price row
            row = table.AddRow();
            row.Cells[0].MergeRight = 2;
            row.Cells[0].AddParagraph("Total");
            row.Cells[3].AddParagraph(totalPrice.ToString("F2"));

            // Render document to PDF
            var pdfRenderer = new PdfDocumentRenderer(true) { Document = document };
            pdfRenderer.RenderDocument();

            using (var ms = new MemoryStream())
            {
                pdfRenderer.PdfDocument.Save(ms);
                return ms.ToArray();
            }
        }

    }
}

