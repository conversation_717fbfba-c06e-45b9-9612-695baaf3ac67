﻿using Admin.TaskDotNet.Dtos;
using Comman.Services.Interfaces;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TaskDotNet.Models;
using Admin.TaskDotNet.Helper;
using Microsoft.Extensions.Hosting;

namespace Admin.TaskDotNet.Controllers
{
    [Authorize(Roles = "Admin,Moderator")]
    public class InventoryItemsController : Controller
    {
        #region Ctor
        private readonly IMapper mapper;
        private readonly IInventoryService inventoryService;
        private readonly IFileManagerService _fileManagerService;

        public InventoryItemsController(IMapper mapper, IInventoryService inventoryService, IFileManagerService fileManagerService)
        {
            this.mapper = mapper;
            this.inventoryService = inventoryService;
            _fileManagerService = fileManagerService;
        }
        #endregion

        #region Index
        public async Task<IActionResult> Index()
        {
            var data = await inventoryService.GetInventoryItemHelper();
            var model = mapper.Map<IEnumerable<InventoryItemHelperDto>>(data);
            return View(model);
        }
        #endregion

        #region Create Inventory item 
        [HttpGet]
        public async Task<IActionResult> Create()
        {
            var Inventorydata = await inventoryService.Get();
            var model = mapper.Map<IEnumerable<InventoryDto>>(Inventorydata);
            ViewBag.Inventory = model;
            return View();
        }
        [HttpPost]
        public async Task<IActionResult> Create(InventoryItemHelperDto dto)
        {
            try
            {
                dto.PhotoName = _fileManagerService.UploadFile(dto.Photo);

                var data = mapper.Map<InventoryItem>(dto);
                await inventoryService.CreateInventoryItemHelper(data);

                if (data != null)
                {
                    // TempData[key: "InventoryCreated"] = "done";
                    return RedirectToAction("Index");
                }
            }
            catch (Exception)
            {
                TempData[key: "ErrorMessage"] = "error";
                return View();

            }
            TempData[key: "ErrorMessage"] = "error";
            return View();


        }
        #endregion

        #region Edit Inventory Item 
        [HttpGet]
        public async Task<IActionResult> Edit(int id)
        {
            var data = await inventoryService.GetInventoryItemById(id);
            var model = mapper.Map<InventoryItemHelperDto>(data);
            var Inventorydata = await inventoryService.Get();
            var Inventorymodel = mapper.Map<IEnumerable<InventoryDto>>(Inventorydata);
            ViewBag.Inventory = Inventorymodel;
            return View(model);
        }
        [HttpPost]
        public IActionResult Edit(InventoryItemHelperDto dto)
        {
            try
            {
                dto.PhotoName = UpdateExistingFile(dto.PhotoName,dto.Photo);

                var data = mapper.Map<InventoryItem>(dto);
                inventoryService.UpdateInventoryItem(data);

                if (data != null)
                {
                    return RedirectToAction("Index");
                }
            }
            catch (Exception)
            {
                TempData[key: "ErrorMessage"] = "error";
                return View();

            }
            TempData[key: "ErrorMessage"] = "error";
            return View();


        }
        #endregion

        #region Delete Inventory Item 
        public async Task<IActionResult> Delete(int id)
        {
            var data = await inventoryService.GetInventoryItemById(id);
            if (data != null)
            {
                _fileManagerService.DeleteFile(data.PhotoName);

                inventoryService.DeleteInventoryItem(data);
                return Json(true);

            }
            TempData[key: "ErrorMessage"] = "error";
            return Json(false);
        }
        #endregion



        private string UpdateExistingFile(string fileUrl, IFormFile? newImageFile)
        {
            if (newImageFile != null)
            {
                if (!string.IsNullOrEmpty(fileUrl))
                    _fileManagerService.DeleteFile(fileUrl);

                fileUrl = _fileManagerService.UploadFile(newImageFile);
            }
            return fileUrl;
        }
    }
}
