﻿@using Admin.TaskDotNet.Dtos
@model List<TaskDotNet.Models.Activity>
@using Comman.Helper.Extensions
@using TaskDotNet.Helper
@{
    ViewData["Title"] = "New Activities";

    var confirmMarkTitle = @SharedLocalizer["Mark as Checked"];
    var confirmMarkText = @SharedLocalizer["Are you sure you want to mark this activity as checked?"];
    var confirmDeleteTitle = @SharedLocalizer["Delete Activity"];
    var confirmDeleteText = @SharedLocalizer["Are you sure you want to delete this activity? This action cannot be undone!"];
    var confirmButtonText = @SharedLocalizer["Yes"];
    var cancelButtonText = @SharedLocalizer["Cancel"];
}

@section Links {
    <link src="https://cdn.datatables.net/2.0.1/css/dataTables.bootstrap5.css" />

    <style>
        .activity-table {
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

            .activity-table th {
                background-color: #f8f9fa;
                font-weight: 600;
                border-bottom: 2px solid #dee2e6;
                padding: 12px;
            }

            .activity-table td {
                padding: 12px;
                border-bottom: 1px solid #dee2e6;
                vertical-align: middle;
            }

            .activity-table tbody tr:hover {
                background-color: #f8f9fa;
            }

        .btn-action {
            margin: 0 2px;
            padding: 6px 12px;
            font-size: 12px;
        }

        .activity-type-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 500;
        }
    </style>
}

<div class="content-wrapper">
    <div class="container-fluid flex-grow-1">
        <h3 class="fw-bold text-white py-3 mb-4">
            <span>@SharedLocalizer["New Activities"]</span>
        </h3>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">@SharedLocalizer["New Activities"] (@Model.Count)</h5>
                    </div>
                    <div class="card-body">
                        @if (Model.Any())
                        {
                            <div class="table-responsive">
                                <table id="activitiesTable" class="table activity-table mb-0">
                                    <thead>
                                        <tr>
                                            <th>@SharedLocalizer["ActivityType"]</th>
                                            <th>@SharedLocalizer["Name"]</th>
                                            <th>@SharedLocalizer["Street"]</th>
                                            <th>@SharedLocalizer["PostBox"]</th>
                                            <th>@SharedLocalizer["City"]</th>
                                            <th>@SharedLocalizer["Kanton"]</th>
                                            <th>@SharedLocalizer["Phone"]</th>
                                            <th>@SharedLocalizer["Email"]</th>
                                            <th>@SharedLocalizer["Date"]</th>
                                            <th>@SharedLocalizer["Actions"]</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var activity in Model)
                                        {
                                            <tr>
                                                <td>
                                                    <span class="activity-type-badge bg-primary text-white">
                                                        @activity.ActivityType.GetDisplayName()
                                                    </span>
                                                </td>
                                                <td>@activity.Name</td>
                                                <td>@activity.Street</td>
                                                <td>@activity.PostBox</td>
                                                <td>@activity.City</td>
                                                <td>@activity.Kanton</td>
                                                <td>@activity.Phone</td>
                                                <td>@activity.Email</td>
                                                <td>
                                                    @if (activity.ActivityType == ActivityType.Cleaning)
                                                    {
                                                        @activity.CleaningDate.ToString("dd.MM.yyyy")
                                                    }
                                                    else
                                                    {
                                                        @activity.MovingDate.ToString("dd.MM.yyyy")
                                                    }
                                                </td>
                                                <td>
                                                    @if (User.IsInRole("Admin"))
                                                    {
                                                        <button class="btn btn-success btn-action mark-checked-btn"
                                                                data-url="@Url.Action("MarkAsChecked", "NewActivities", new { id = activity.Id })"
                                                                title="@SharedLocalizer["Mark as Checked"]">
                                                            <i class="bx bx-check"></i>
                                                        </button>
                                                        <button class="btn btn-danger btn-action delete-activity-btn"
                                                                data-url="@Url.Action("DeleteActivity", "NewActivities", new { id = activity.Id })"
                                                                title="@SharedLocalizer["Delete"]">
                                                            <i class="bx bx-trash"></i>
                                                        </button>
                                                    }
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-5">
                                <i class="bx bx-check-circle" style="font-size: 4rem; color: #28a745;"></i>
                                <h4 class="mt-3">@SharedLocalizer["No New Activities"]</h4>
                                <p class="text-muted">@SharedLocalizer["All activities have been checked."]</p>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.datatables.net/2.0.1/js/dataTables.js"></script>
    <script src="https://cdn.datatables.net/2.0.1/js/dataTables.bootstrap5.js"></script>

    <script>
        $(document).ready(function () {
            new DataTable('#activitiesTable');
            // Handle Mark as Checked button click
            $('.mark-checked-btn').on('click', function (e) {
                e.preventDefault();
                var url = $(this).data('url');

                Swal.fire({
                    title: "@Html.Raw(confirmMarkTitle)",
                    text: "@Html.Raw(confirmMarkText)",
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#28a745',
                    cancelButtonColor: '#6c757d',
                    confirmButtonText: "@Html.Raw(confirmButtonText)",
                    cancelButtonText: "@Html.Raw(cancelButtonText)"
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Create a form and submit it
                        var form = $('<form method="post" action="' + url + '"></form>');
                        form.append('@Html.AntiForgeryToken()');
                        $('body').append(form);
                        form.submit();
                    }
                });
            });

            // Handle Delete Activity button click
            $('.delete-activity-btn').on('click', function (e) {
                e.preventDefault();
                var url = $(this).data('url');

                Swal.fire({
                    title: "@Html.Raw(confirmDeleteTitle)",
                    text: "@Html.Raw(confirmDeleteText)",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#dc3545',
                    cancelButtonColor: '#6c757d',
                    confirmButtonText: "@Html.Raw(confirmButtonText)",
                    cancelButtonText: "@Html.Raw(cancelButtonText)"
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Create a form and submit it
                        var form = $('<form method="post" action="' + url + '"></form>');
                        form.append('@Html.AntiForgeryToken()');
                        $('body').append(form);
                        form.submit();
                    }
                });
            });
        });
    </script>
}
