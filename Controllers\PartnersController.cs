﻿using Admin.TaskDotNet.Dtos;
using Comman.Services.Interfaces;
using Admin.TaskDotNet.Services;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using TaskDotNet.Helper;
using TaskDotNet.Models;
using TaskDotNet.Comman.DataAccess;
using Microsoft.Extensions.Localization;
using TaskDotNet.Localization;
namespace Admin.TaskDotNet.Controllers
{
    [Authorize(Roles = "Admin,Moderator")]
    public class PartnersController : Controller
    {
        #region ctor
        private readonly UserManager<Partner> userManager;
        private readonly ApplicationDbContext _context;
        private readonly IMailService mailService;
        private readonly IEmailTextService emailTextService;
        private readonly IMapper _mapper;
        private readonly IStringLocalizer<SharedResource> SharedLocalizer;
        public PartnersController(UserManager<Partner> userManager, ApplicationDbContext context, IMapper mapper, IMailService mailService, IEmailTextService emailTextService, IStringLocalizer<SharedResource> sharedLocalizer)
        {
            this.userManager = userManager;
            _context = context;
            _mapper = mapper;
            this.mailService = mailService;
            this.emailTextService = emailTextService;
            SharedLocalizer = sharedLocalizer;
        }
        #endregion

        #region All Partners
        public async Task<IActionResult> AllPartners()
        {
            var partners = await userManager.GetUsersInRoleAsync("Partner");
            return View(partners);
        }
        #endregion

        #region Delete Partner
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> Delete(string id)
        {
            var partner = await userManager.FindByIdAsync(id);
            if (partner == null)
            {
                return NotFound();
            }
            var result = await userManager.DeleteAsync(partner);

            if (!result.Succeeded)
            {
                TempData[key: "ErrorMessage"] = "error";
            }

            return RedirectToAction("AllPartners");

        }
        #endregion

        #region Edit Paertner Data
        [HttpGet]
        public async Task<IActionResult> Edit(string email)
        {
            var partner = await _context.Partners
                .AsNoTracking()
                .Include(p => p.PartnerCountries)
                .Include(p => p.PartnerActivities)
                .FirstOrDefaultAsync(p => p.Email == email);

            if (partner == null)
            {
                return NotFound();
            }

            partner.PartnerCountries ??= new PartnerCountries();
            partner.PartnerActivities ??= new PartnerActivities();

            ViewBag.PartnerCountries = SplitKantons(partner.PartnerCountries);
            ViewBag.PartnerActivities = PartnerHelper.GetBooleanProperties(partner.PartnerActivities);
            // **Mapping the partner entity to the PartnerDto**
            var model = _mapper.Map<PartnerDto>(partner);
            model.Blocked = partner.Status == PartnerStatus.Blocked;

            return View(model);
        }
        [HttpPost]
        [Authorize(Roles = "Admin")]

        public async Task<IActionResult> Edit(PartnerDto partnerDto)
        {
            if (!ModelState.IsValid)
            {
                ViewBag.PartnerCountries = SplitKantons(partnerDto.PartnerCountries);
                ViewBag.PartnerActivities = PartnerHelper.GetBooleanProperties(partnerDto.PartnerActivities);

                return View(partnerDto);
            }

            // Get the current authenticated partner with related entities
            var partner = await _context.Partners
                .AsNoTracking()
                .Include(p => p.PartnerCountries)
                .Include(p => p.PartnerActivities)
                .FirstOrDefaultAsync(p => p.Id == partnerDto.Id);

            if (partner == null)
            {
                return NotFound();
            }

            // Map the PartnerDto to the Partner entity
            _mapper.Map(partnerDto, partner);

            partner.Status = partnerDto.Blocked ? PartnerStatus.Blocked : PartnerStatus.Active;
            partner.IsCompletedData = true;

            _context.Partners.Update(partner);
            // Directly save changes
            await _context.SaveChangesAsync();

            TempData["success"] = SharedLocalizer["Partner Data Updated Successfully"].Value;
            return RedirectToAction("AllPartners");

        }
        #endregion

        #region SendEmail

        public async Task<IActionResult> SendEmail(string id)
        {
            var userModel = await userManager.FindByIdAsync(id);
            if (userModel == null)
                return NotFound();

            var textData = await emailTextService.Get();
            ViewBag.EmailTexts = textData.Select(m => new SelectListItem
            {
                Text = m.Title,
                Value = m.Id.ToString()
            }).ToList();


            return View(new EmailDto { Email = userModel.Email, Name = userModel.CompanyName, Postbox = userModel.PPostBox, City = userModel.PCity,Language = userModel.Language });
        }
        [HttpPost]
        public async Task<IActionResult> SendEmail(EmailDto dto)
        {
            if (!ModelState.IsValid)
            {
                var textData = await emailTextService.Get();
                ViewBag.EmailTexts = textData.Select(m => new SelectListItem
                {
                    Text = m.Title,
                    Value = m.Id.ToString()
                }).ToList();

                return View(dto);
            }

            MailRequest mailRequest = new MailRequest
            {
                ToEmail = dto.Email,
                Subject = dto.Title,
                Body = dto.Message,

            };

            await mailService.SendEmailAsync(mailRequest, default);

            return RedirectToAction("AllPartners");
        }
        #endregion

        #region Partner Orders and Balance History
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> PartnerOrders(string id)
        {
            var partner = await userManager.FindByIdAsync(id);
            if (partner == null)
            {
                return NotFound();
            }

            var movements = await _context.PayOrders
                .Where(m => m.PartnerId == id)
                .ToListAsync();

            ViewBag.PartnerName = partner.CompanyName;
            ViewBag.PartnerId = id;

            return View(movements);
        }
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> PartnerBalanceHistory(string id)
        {
            var partner = await userManager.FindByIdAsync(id);
            if (partner == null)
            {
                return NotFound();
            }

            var balanceMovements = await _context.BalanceMovements
                .Where(p => p.PartnerId == id)
                .ToListAsync();

            ViewBag.PartnerName = partner.CompanyName;
            ViewBag.CurrentBalance = partner.Saldo;
            ViewBag.PartnerId = id;

            return View(balanceMovements);
        }


        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> DeleteOrder(int id, string partnerId)
        {
            var movement = await _context.PayOrders.FindAsync(id);
            if (movement == null)
            {
                return NotFound();
            }

            _context.PayOrders.Remove(movement);
            await _context.SaveChangesAsync();

            return RedirectToAction("PartnerOrders", new { id = partnerId });
        }

        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> DeleteBalanceRecharge(int id, string partnerId)
        {
            var balanceMovements = await _context.BalanceMovements.FindAsync(id);
            if (balanceMovements == null)
            {
                return NotFound();
            }

            _context.BalanceMovements.Remove(balanceMovements);
            await _context.SaveChangesAsync();

            return RedirectToAction("PartnerBalanceHistory", new { id = partnerId });
        }

        [HttpGet]
        public async Task<IActionResult> PrintOrdersReport(string id, DateTime? startDate, DateTime? endDate)
        {
            var partner = await userManager.FindByIdAsync(id);
            if (partner == null)
            {
                return NotFound();
            }

            var query = _context.PayOrders.Where(m => m.PartnerId == id);

            if (startDate.HasValue)
            {
                query = query.Where(m => m.PayDate >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(m => m.PayDate <= endDate.Value);
            }

            var movements = await query.ToListAsync();

            ViewBag.PartnerName = partner.CompanyName;
            ViewBag.PartnerAddress = $"{partner.PStreet}, {partner.PPostBox} {partner.PCity}";
            ViewBag.StartDate = startDate;
            ViewBag.EndDate = endDate;
            ViewBag.IsPrintView = true;
            ViewBag.PartnerId = id;

            return View("PartnerOrdersPrint", movements);
        }
        #endregion

        #region Helper
        private List<CountryGroup> SplitKantons(PartnerCountries partnerCountries)
        {
            var countryGroups = new List<CountryGroup>
            {
                new CountryGroup
                {
                    LandCode = "CH",
                    LandName = "Switzerland",
                    Countries = new List<(string DisplayName,string Name, bool Value)>
                    {
                        ("Aargau",nameof(partnerCountries.Aargau) ,partnerCountries.Aargau),
                        ("Appenzell Ausserrhoden",nameof(partnerCountries.AppenzellAusserrhoden) ,partnerCountries.AppenzellAusserrhoden),
                        ("Appenzell Innerrhoden",nameof(partnerCountries.AppenzellInnerrhoden) ,partnerCountries.AppenzellInnerrhoden),
                        ("Basel-Landschaft",nameof(partnerCountries.Basel_Landschaft) ,partnerCountries.Basel_Landschaft),
                        ("Basel-Stadt",nameof(partnerCountries.Basel_Stadt) ,partnerCountries.Basel_Stadt),
                        ("Bern",nameof(partnerCountries.Bern) ,partnerCountries.Bern),
                        ("Freiburg (Fribourg)",nameof(partnerCountries.Freiburg) ,partnerCountries.Freiburg),
                        ("Genf (Genève)",nameof(partnerCountries.Genf) ,partnerCountries.Genf),
                        ("Glarus",nameof(partnerCountries.Glarus) ,partnerCountries.Glarus),
                        ("Graubünden (Grisons)",nameof(partnerCountries.Graubünden) ,partnerCountries.Graubünden),
                        ("Jura",nameof(partnerCountries.Jura) ,partnerCountries.Jura),
                        ("Luzern",nameof(partnerCountries.Luzern) ,partnerCountries.Luzern),
                        ("Neuenburg (Neuchâtel)",nameof(partnerCountries.Neuenburg) ,partnerCountries.Neuenburg),
                        ("Nidwalden",nameof(partnerCountries.Nidwalden) ,partnerCountries.Nidwalden),
                        ("Obwalden",nameof(partnerCountries.Obwalden) ,partnerCountries.Obwalden),
                        ("Schaffhausen",nameof(partnerCountries.Schaffhausen) ,partnerCountries.Schaffhausen),
                        ("Schwyz",nameof(partnerCountries.Schwyz) ,partnerCountries.Schwyz),
                        ("Solothurn",nameof(partnerCountries.Solothurn) ,partnerCountries.Solothurn),
                        ("St. Gallen",nameof(partnerCountries.St_Gallen) ,partnerCountries.St_Gallen),
                        ("Tessin (Ticino)",nameof(partnerCountries.Tessin) ,partnerCountries.Tessin),
                        ("Thurgau",nameof(partnerCountries.Thurgau) ,partnerCountries.Thurgau),
                        ("Uri",nameof(partnerCountries.Uri) ,partnerCountries.Uri),
                        ("Waadt (Vaud)",nameof(partnerCountries.Waadt) ,partnerCountries.Waadt),
                        ("Wallis (Valais)",nameof(partnerCountries.Wallis) ,partnerCountries.Wallis),
                        ("Zug",nameof(partnerCountries.Zug) ,partnerCountries.Zug),
                        ("Zürich",nameof(partnerCountries.Zürich) ,partnerCountries.Zürich),
                        ("Liechtenstein",nameof(partnerCountries.Lichtenstein) ,partnerCountries.Lichtenstein)
                    }
                },
                new CountryGroup
                {
                    LandCode = "AT",
                    LandName = "Austria",
                    Countries = new List<(string DisplayName,string Name, bool Value)>
                    {
                        ("Burgenland",nameof(partnerCountries.Burgenland) ,partnerCountries.Burgenland),
                        ("Kärnten",nameof(partnerCountries.Kärnten) ,partnerCountries.Kärnten),
                        ("Niederösterreich",nameof(partnerCountries.Niederösterreich) ,partnerCountries.Niederösterreich),
                        ("Oberösterreich",nameof(partnerCountries.Oberösterreich) ,partnerCountries.Oberösterreich),
                        ("Salzburg",nameof(partnerCountries.Salzburg) ,partnerCountries.Salzburg),
                        ("Steiermark",nameof(partnerCountries.Steiermark) ,partnerCountries.Steiermark),
                        ("Tirol",nameof(partnerCountries.Tirol) ,partnerCountries.Tirol),
                        ("Vorarlberg",nameof(partnerCountries.Vorarlberg) ,partnerCountries.Vorarlberg),
                        ("Wien",nameof(partnerCountries.Wien) ,partnerCountries.Wien)
                    }
                },
                new CountryGroup
                {
                    LandCode = "DE",
                    LandName = "Germany",
                    Countries = new List<(string DisplayName,string Name, bool Value)>
                    {
                        ("Baden-Württemberg",nameof(partnerCountries.Baden_Württemberg) ,partnerCountries.Baden_Württemberg),
                        ("Bayern",nameof(partnerCountries.Bayern) ,partnerCountries.Bayern),
                        ("Berlin",nameof(partnerCountries.Berlin) ,partnerCountries.Berlin),
                        ("Brandenburg",nameof(partnerCountries.Brandenburg) ,partnerCountries.Brandenburg),
                        ("Bremen",nameof(partnerCountries.Bremen) ,partnerCountries.Bremen),
                        ("Hamburg",nameof(partnerCountries.Hamburg) ,partnerCountries.Hamburg),
                        ("Hessen",nameof(partnerCountries.Hessen) ,partnerCountries.Hessen),
                        ("Mecklenburg-Vorpommern",nameof(partnerCountries.Mecklenburg_Vorpommern) ,partnerCountries.Mecklenburg_Vorpommern),
                        ("Niedersachsen",nameof(partnerCountries.Niedersachsen) ,partnerCountries.Niedersachsen),
                        ("Nordrhein-Westfalen",nameof(partnerCountries.Nordrhein_Westfalen) ,partnerCountries.Nordrhein_Westfalen),
                        ("Rheinland-Pfalz",nameof(partnerCountries.Rheinland_Pfalz) ,partnerCountries.Rheinland_Pfalz),
                        ("Saarland",nameof(partnerCountries.Saarland) ,partnerCountries.Saarland),
                        ("Sachsen",nameof(partnerCountries.Sachsen) ,partnerCountries.Sachsen),
                        ("Sachsen-Anhalt",nameof(partnerCountries.Sachsen_Anhalt) ,partnerCountries.Sachsen_Anhalt),
                        ("Schleswig-Holstein",nameof(partnerCountries.Schleswig_Holstein) ,partnerCountries.Schleswig_Holstein),
                        ("Thüringen",nameof(partnerCountries.Thüringen) ,partnerCountries.Thüringen)
                    }
                }
            };


            return countryGroups;
        }

        #endregion
    }


}
