﻿@model ContactUs


<style>
    .bg-gradient-primary {
        background: linear-gradient(90deg, #66a1a1, #2f7c7c);
    }

    .card-header p {
        font-size: 0.9rem;
        margin-top: 5px;
        color: rgba(255, 255, 255, 0.75);
    }

    .text-muted {
        font-weight: 600;
    }

    .btn-outline-secondary {
        color: #6c757d;
        border: 1px solid #6c757d;
    }

        .btn-outline-secondary:hover {
            background: #6c757d;
            color: #fff;
        }

</style>


<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-lg border-0">
                <!-- Header Section -->
                <div class="card-header bg-gradient-primary text-white text-center py-4">
                    <h2 class="card-title mb-0">@SharedLocalizer["ContactDetails"]</h2>
                    <p class="card-text">@SharedLocalizer["ContactSubTitle"]</p>
                </div>

                <!-- Body Section -->
                <div class="card-body px-5 py-4">
                    <!-- Profile Section -->
                    <div class="text-center mb-4">
                        <div class="rounded-circle bg-light border" style="width: 100px; height: 100px; margin: 0 auto;">
                            <i class="bx bxs-user text-primary" style="font-size: 48px; line-height: 100px;"></i>
                        </div>
                        <h4 class="mt-3 mb-0">@Model.Name</h4>
                        <small class="text-muted">Contact ID: @Model.Id</small>
                    </div>

                    <!-- Details Section -->
                    <div class="row mb-3">
                        <div class="col-sm-4 text-muted">@SharedLocalizer["Email"]:</div>
                        <div class="col-sm-8">@Model.Email</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-4 text-muted">@SharedLocalizer["Phone"]:</div>
                        <div class="col-sm-8">@Model.PhoneNumber</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-4 text-muted">@SharedLocalizer["Message"]:</div>
                        <div class="col-sm-8">@Model.Message</div>
                    </div>
                </div>

                <!-- Footer Section -->
                <div class="card-footer bg-light text-center py-3">
                    <a href="@Url.Action("Index", "ContactUs")" class="btn btn-outline-secondary me-3">
                        <i class='bx bx-arrow-back'></i> @SharedLocalizer["BackToList"]
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
