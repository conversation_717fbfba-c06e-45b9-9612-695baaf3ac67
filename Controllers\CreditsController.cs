using Admin.TaskDotNet.Dtos;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TaskDotNet.Comman.DataAccess;
using TaskDotNet.Models;
using Microsoft.Extensions.Localization;
using TaskDotNet.Localization;
using Comman.Helper.Extensions;

namespace Admin.TaskDotNet.Controllers
{
    [Authorize(Roles = "Admin")]
    public class CreditsController : Controller
    {
        #region Ctor
        private readonly ApplicationDbContext _context;
        private readonly UserManager<Partner> _userManager;
        private readonly IStringLocalizer<SharedResource> _localizer;

        public CreditsController(ApplicationDbContext context, UserManager<Partner> userManager, IStringLocalizer<SharedResource> localizer)
        {
            _context = context;
            _userManager = userManager;
            _localizer = localizer;
        }
        #endregion

        #region Index
        [HttpGet]
        public IActionResult Index()
        {
            var model = new CreditsDto
            {
                DateFrom = DateTime.UtcNow.AddMonths(-6).Date,
                DateTo = DateTime.UtcNow.Date
            };

            ViewBag.Credits = new List<CreditItemDto>();
            ViewBag.TotalAmount = 0m;

            return View(model);
        }

        [HttpPost]
        public async Task<IActionResult> Index(CreditsDto model)
        {
            if (!ModelState.IsValid)
            {
                ViewBag.Credits = new List<CreditItemDto>();
                ViewBag.TotalAmount = 0m;
                return View(model);
            }

            // Get all PayOrders within the date range with partner information
            var payOrders = await _context.BalanceMovements
                .Where(po => po.CreatedAt >= model.DateFrom && po.CreatedAt <= model.DateTo)
                .Include(po => po.Partner)
                .OrderByDescending(po => po.CreatedAt)
                .ToListAsync();

            var credits = payOrders.Select(po => new CreditItemDto
            {
                PartnerName = po.Partner?.CompanyName ?? "Unknown Partner",
                PayDate = po.CreatedAt,
                Amount = po.Amount,
                PaymentMethod = po.PaymentMethod,
                
            }).ToList();

            ViewBag.Credits = credits;
            ViewBag.TotalAmount = credits.Sum(c => c.Amount);

            return View(model);
        }

        [HttpGet]
        public async Task<IActionResult> PrintCreditsReport(DateTime dateFrom, DateTime dateTo)
        {
            // Get all PayOrders within the date range with partner information
            var payOrders = await _context.BalanceMovements
                .Where(po => po.CreatedAt >= dateFrom && po.CreatedAt <= dateTo)
                .Include(po => po.Partner)
                .OrderByDescending(po => po.CreatedAt)
                .ToListAsync();

            var credits = payOrders.Select(po => new CreditItemDto
            {
                PartnerName = po.Partner?.CompanyName ?? "Unknown Partner",
                PayDate = po.CreatedAt,
                Amount = po.Amount,
                PaymentMethod = po.PaymentMethod,
            }).ToList();

            ViewBag.DateFrom = dateFrom;
            ViewBag.DateTo = dateTo;
            ViewBag.TotalAmount = credits.Sum(c => c.Amount);
            ViewBag.IsPrintView = true;

            return View(credits);
        }
        #endregion
    }
}
