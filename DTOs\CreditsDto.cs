using System.ComponentModel.DataAnnotations;

namespace Admin.TaskDotNet.Dtos
{
    public class CreditsDto
    {
        [Display(Name = "DateFrom")]
        public DateTime DateFrom { get; set; } = DateTime.UtcNow.AddMonths(-6).Date;

        [Display(Name = "DateTo")]
        public DateTime DateTo { get; set; } = DateTime.UtcNow.Date;
    }

    public class CreditItemDto
    {
        public string PartnerName { get; set; }
        public DateTime PayDate { get; set; }
        public decimal Amount { get; set; }
        public string PaymentMethod { get; set; }
    }
}
