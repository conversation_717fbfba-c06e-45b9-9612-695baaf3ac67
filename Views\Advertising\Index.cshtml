﻿@model IEnumerable<Advertising>

@{
    ViewData["Title"] = "Index";

    var confirmDeleteTitle = @SharedLocalizer["Delete record"];

    var confirmDeleteText2 = @SharedLocalizer["Are you sure you want to delete the selected entry?"];

    var yesDeleteText = @SharedLocalizer["Yes"];

    var noDeleteText = @SharedLocalizer["No"];
}

<script>
    // Pass the translated text to JavaScript variables

    var confirmDeleteTitle = '@confirmDeleteTitle';
    var confirmDeleteText2 = '@Html.Raw(confirmDeleteText2)';
    var yesDeleteText = '@yesDeleteText';
    var noDeleteText = '@noDeleteText';
</script>
@section Links {
    <link src="https://cdn.datatables.net/2.0.1/css/dataTables.bootstrap5.css" />
    <!-- Bootstrap CSS -->
    <!-- jQuery (necessary for Bootstrap's JavaScript plugins) -->
    <!-- Bootstrap JS -->
}

<!-- Content wrapper -->
<div class="content-wrapper">
    <!-- Content -->-

    <div class="container-fluid flex-grow-1 ">
        <h3 class="fw-bold text-white py-3 mb-4">
            <span></span>@SharedLocalizer["AdvertisingList"]
        </h3>

        <!-- Basic Bootstrap Table -->
        <div class="card">
            @if (User.IsInRole("Admin"))
            {
                <div class="container">
                    <div class="row justify-content-between align-items-center">
                        <div class="col-md-3">
                            <a class="btn btn-primary w-100 my-4" asp-controller="Advertising" asp-action="Create"><i class='bx bx-plus'></i> @SharedLocalizer["Create entry"] </a>
                        </div>
                        
                        <div class="col-md-3 text-md-end">
                            <!-- Upload File Form -->
                            <form method="post" enctype="multipart/form-data" asp-controller="Advertising" asp-action="UploadFile" id="uploadForm" class="d-inline">
                                <!-- Hidden File Input -->
                                <input type="file" name="file" id="fileInput" class="d-none" onchange="submitForm()" />

                                <!-- Styled Button to Trigger File Input -->
                                <button type="button" class="btn btn-warning w-100" onclick="document.getElementById('fileInput').click();">
                                    <i class='bx bx-upload'></i> @SharedLocalizer["Upload File"]
                                </button>

                                <!-- Hidden Submit Button -->
                                <button type="submit" class="d-none" id="submitButton"></button>
                            </form>
                        </div>

                        <div class="col-md-3 text-md-end">
                            <!-- Download Template Button -->
                            <a class="btn btn-secondary w-100 " id="downloadButton">
                                <i class='bx bx-download'></i> @SharedLocalizer["ExportExcel"]
                            </a>
                        </div>

                        <div class="col-md-3 text-md-end">
                            <a class="btn btn-info w-100 my-4" asp-controller="Advertising" asp-action="SendEmail"><i class='bx bx-envelope'></i> @SharedLocalizer["Send"] </a>
                        </div>
                    </div> 
                </div>

            }

            <div class="table-responsive text-nowrap container">
                <table id="example" class="table table-striped" style="width:100%">
                    <thead>
                        <tr>
                            <th>@SharedLocalizer["Branch"]</th>
                            <th>@SharedLocalizer["Company"]</th>
                            <th>@SharedLocalizer["BoxCity"]</th>
                            <th>@SharedLocalizer["Email"]</th>
                            <th>@SharedLocalizer["Versand"]</th>
                            <th>@SharedLocalizer["Status"]</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody class="table-border-bottom-0">
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td> @item.Branch </td>
                                <td>@item.Company</td>
                                <td>@item.BoxCity</td>
                                <td> @item.Email </td>
                                <td> @item.Versand?.ToString("dd.MM.yyyy") </td>
                                <td>
                                    @if (item.Status == "Success")
                                    {
                                        <span class="badge bg-label-success me-1">@item.Status</span>
                                    }
                                    else if (item.Status == "Failed")
                                    {
                                        <span class="badge bg-label-danger me-1">@item.Status</span>
                                    }
                                </td>
                                <td class="text-center">
                                    @if (User.IsInRole("Admin"))

                                    {
                                        <a class="btn btn-primary" asp-controller="Advertising" asp-action="Edit" asp-route-id="@item.Id"><i class="bx bx-edit-alt me-1"></i>@SharedLocalizer["Edit"] </a>
                                        <button class="btn btn-danger delete-btn" data-url="@Url.Action("Delete", "Advertising", new { id = item.Id })">
                                            @SharedLocalizer["Delete"]
                                        </button>
                                    }
                                </td>

                            </tr>
                        }


                    </tbody>
                </table>
            </div>
        </div>
        <!--/ Basic Bootstrap Table -->

    </div>
</div>
@section Scripts {


    <script src="https://cdn.datatables.net/2.0.1/js/dataTables.js"></script>
    <script src="https://cdn.datatables.net/2.0.1/js/dataTables.bootstrap5.js"></script>


    <script>

        $(document).ready(function () {
            new DataTable('#example');
        });

        document.getElementById('downloadButton').addEventListener('click', function () {
            // Show the loader
            document.getElementById('loader').classList.remove('d-none');

            // Simulate the download process
            setTimeout(function () {
                window.location.href = '/Advertising/ExportToExcel'; // Replace with your actual URL

                // Hide the loader after a delay (e.g., 3 seconds)
                setTimeout(function () {
                    loader.classList.add('d-none');
                }, 3000); // Adjust the delay as needed
            }, 100); // Small delay to ensure the loader appears before navigation
        });

        function submitForm() {
            // Show the loader
            document.getElementById('loader').classList.remove('d-none');

            // Programmatically submit the form
            document.getElementById('uploadForm').submit();
        }

    </script>


}




