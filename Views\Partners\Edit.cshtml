﻿@using Admin.TaskDotNet.Dtos
@using TaskDotNet.Helper
@using Admin.TaskDotNet.Helper.Extensions
@model PartnerDto
@{
    ViewData["Title"] = "Edit";
}

@{
    var countryGroups = ViewBag.PartnerCountries as List<CountryGroup>;
}

@section Links {
    <link href="~/lib/jquery-ui/jquery-ui.css" rel="stylesheet" />
    <style>
        .ui-autocomplete {
            max-height: 300px;
            overflow: hidden scroll;
        }
    </style>
}

<!-- Content wrapper -->
<div class="content-wrapper">
    <!-- Content -->

    <div class="container-fluid flex-grow-1 ">
        <h3 class="fw-bold text-white py-3 mb-4">
            <span>@SharedLocalizer["Add"]  /</span> @SharedLocalizer["Edit partner profile"]
        </h3>

        <div class="row">
            <div class="col-md-12">
                <div class="card mb-4">
                    <form asp-controller="Partners" asp-action="Edit">
                        <div class="d-flex align-items-center justify-content-between px-3">
                            <h5 class="card-header col-md-4">@SharedLocalizer["Account Settings"] </h5>
                            <div class="col-md-4 row">
                                <label asp-for="Saldo" class="col-sm-3 col-form-label">@SharedLocalizer["Balance"]</label>
                                <div class="col-sm-9">
                                    <input asp-for="Saldo" class="form-control" style="text-align:right;" />
                                </div>
                            </div>
                        </div>
                        <!-- Account -->


                        <hr class="my-0" />
                        <div class="card-body">
                            <div asp-validation-summary="ModelOnly" class="text-danger"> </div>
                            <input type="hidden" asp-for="Id" />
                            <div class="row justify-content-center">
                                <div class="mb-3 col-md-4">
                                    <label asp-for="CompanyName" class="form-label">@SharedLocalizer["Company Name"]</label>
                                    <input class="form-control" asp-for="CompanyName" />
                                    <span asp-validation-for="CompanyName" class="text-danger"></span>
                                </div>
                                <div class="mb-3 col-md-4">
                                    <label class="form-label" asp-for="Land">@SharedLocalizer["Land"]</label>
                                    <select asp-for="Land" class="select2 form-select" asp-items="Html.GetEnumSelectListAsString<Country>()">
                                    </select>
                                </div>
                                <div class="mb-3 col-md-4">
                                    <label class="form-label" asp-for="Evaluation">@SharedLocalizer["Evaluation"]</label>
                                    <select asp-for="Evaluation" class="select2 form-select" asp-items="Html.GetEnumSelectList<PartnerEvaluation>()">
                                    </select>
                                </div>
                                <div class="mb-3 col-md-4">
                                    <label class="form-label" asp-for="PStreet">@SharedLocalizer["Street"]</label>
                                    <input asp-for="PStreet" class="form-control" />
                                    <span asp-validation-for="PStreet" class="text-danger"></span>
                                </div>
                                <div class="mb-3 col-md-4">
                                    <label asp-for="PPostBox" class="form-label">@SharedLocalizer["PostBox"]</label>
                                    <input asp-for="PPostBox" class="form-control" />
                                    <span asp-validation-for="PPostBox" class="text-danger"></span>
                                </div>
                                <div class="mb-3 col-md-4">
                                    <label asp-for="PCity" class="form-label">@SharedLocalizer["City"]</label>
                                    <input asp-for="PCity" class="form-control" />
                                    <span asp-validation-for="PCity" class="text-danger"></span>
                                </div>

                                <div class="mb-3 col-md-4">
                                    <label class="form-label" asp-for="UID">@SharedLocalizer["UID"]</label>
                                    <input asp-for="UID" class="form-control" />
                                    <span asp-validation-for="UID" class="text-danger"></span>
                                </div>

                                <div class="mb-3 col-md-4">
                                    <label class="form-label" asp-for="Salute">@SharedLocalizer["Contact person"]</label>
                                    <select asp-for="Salute" class="select2 form-select" asp-items="Html.GetEnumSelectList<Salute>()">
                                    </select>
                                </div>

                                <div class="mb-3 col-md-4">
                                    <label class="form-label" asp-for="PName">Name</label>
                                    <input asp-for="PName" class="form-control" />
                                </div>

                                <div class="mb-3 col-md-4">
                                    <label asp-for="StartDate" class="form-label">@SharedLocalizer["Start date"]</label>
                                    <input asp-for="StartDate" class="form-control flat-picker-date bg-white" />
                                </div>

                                <div class="mb-3 col-md-4">
                                    <label class="form-label" asp-for="Website">@SharedLocalizer["Website"] </label>
                                    <input asp-for="Website" class="form-control" />
                                </div>

                                <div class="mb-3 col-md-4">
                                    <label class="form-label" asp-for="Email">@SharedLocalizer["Email"]</label>
                                    <input asp-for="Email" readonly class="form-control" />
                                </div>

                                <div class="mb-3 col-md-4">
                                    <label class="form-label" asp-for="Phone">@SharedLocalizer["Phone"]</label>
                                    <input asp-for="Phone" class="form-control" />
                                    <span asp-validation-for="Phone" class="text-danger"></span>
                                </div>

                                <div class="mb-3 col-md-4">
                                    <label class="form-label" asp-for="Mobile">@SharedLocalizer["Mobile"] </label>
                                    <input asp-for="Mobile" class="form-control" />
                                </div>

                                <div class="mb-3 col-md-4">
                                    <label asp-for="Language" class="form-label">@SharedLocalizer["Language"]</label>
                                    <select asp-for="Language" class="select2 form-select" asp-items="Html.GetEnumSelectListAsString<Language>()">
                                        <option disabled value="">Select Language</option>
                                    </select>
                                </div>

                                <hr class="w-75 bg-black" />


                                <div class="mb-3 col-md-4">
                                    <label for="Bank" class="form-label">@SharedLocalizer["Bank Name"]</label>
                                    <input asp-for="Bank" class="form-control" />
                                </div>
                                <div class="mb-3 col-md-4">
                                    <label for="IBAN" class="form-label">@SharedLocalizer["IBAN"]</label>
                                    <input asp-for="IBAN" class="form-control" />
                                </div>
                                <div class="mb-3 col-md-4">
                                    <label for="Owner" class="form-label">@SharedLocalizer["Owner"]</label>
                                    <input asp-for="Owner" class="form-control" />
                                </div>

                            </div>

                            <div class="bg-label-primary">
                                <hr class="" />
                                <h5 class="d-flex justify-content-center text-black">@SharedLocalizer["Partner Status"]</h5>
                                <hr class="" />
                            </div>
                            <div class="container">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" asp-for="Blocked" style="height:25px; width:60px; margin-right:20px" />
                                            <label class="form-check-label" asp-for="Blocked">@SharedLocalizer["Blocked"]</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-label-primary">
                                <hr class="" />
                                <h5 class="d-flex justify-content-center text-black">@SharedLocalizer["Select the cantons in which you would like to work here"] </h5>
                                <hr class="" />
                            </div>
                            <input type="hidden" asp-for="PartnerCountries.Id" />
                            <div class="container">
                                <div class="row">
                                    @foreach (var group in countryGroups)
                                    {
                                        <div class="country-group row" data-land="@group.LandCode" style="display: none;">
                                            @foreach (var country in group.Countries)
                                            {
                                                <div class="col-md-4">
                                                    <div class="form-check form-switch mb-3">
                                                        <input class="form-check-input" type="checkbox" id="@country.Name" name="<EMAIL>" value="true" @(country.Value ? "checked" : "")
                                                               style="height:25px; width:60px; margin-right:20px" />
                                                        <label class="form-check-label" for="@country.Name">@country.DisplayName</label>
                                                        <input type="hidden" name="<EMAIL>" value="false" />
                                                    </div>
                                                </div>
                                            }
                                        </div>
                                    }
                                </div>
                            </div>


                            <div class="bg-label-primary">
                                <hr class="" />
                                <h5 class="d-flex justify-content-center text-black">@SharedLocalizer["Select the Activity you want"] </h5>
                                <hr class="" />
                            </div>


                            <div class="container">


                                <div class="row">
                                    <input type="hidden" asp-for="PartnerActivities.Id" />

                                    @if (ViewBag.PartnerActivities != null)

                                    {

                                        foreach ((string DisplayName, string Name, bool Value) activity in ViewBag.PartnerActivities)

                                        {
                                            <div class="form-check form-switch mb-3 col-md-4">
                                                <input class="form-check-input" type="checkbox" id="@activity.Name" name="<EMAIL>" value="true" @(activity.Value ? "checked" : "")
                                                       style="height:25px; width:60px; margin-right:20px" />
                                                <label class="form-check-label" for="@activity.Name">@activity.DisplayName</label>
                                                <input type="hidden" name="<EMAIL>" value="false" />
                                            </div>
                                        }

                                    }
                                </div>

                            </div>

                            <div class="mt-2">
                                @if (User.IsInRole("Admin"))

                                {

                                    <input type="submit" class="btn btn-primary me-2" value="@SharedLocalizer["Save"]" />

                                }
                                <a asp-controller="Partners" asp-action="AllPartners" class="btn btn-outline-secondary">@SharedLocalizer["Cancel"]</a>
                            </div>
                        </div>
                    </form>

                </div>

            </div>
        </div>
    </div>
    <!-- / Content -->
</div>


@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script src="~/lib/jquery-ui/jquery-ui.js"></script>

    <script>
        let postBoxCitiesGlobal = {};

        // Cache selectors to avoid repeated DOM lookups
        const $landInput = $("#Land");
        const $postBoxInput = $("#PPostBox");
        const $cityInput = $("#PCity");

        $(document).ready(function () {
            fetchAllPostBoxCities();
            initializeLandChangeEvent();

            showCorrectCountries();
        });

        // Fetch data and initialize the autocomplete
        function fetchAllPostBoxCities() {
            $.ajax({
                type: "GET",
                url: "/PostBoxCities/AllPostBoxCities",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function (data) {
                    postBoxCitiesGlobal = data;
                    initializeAutocomplete(postBoxCitiesGlobal, $landInput.val());
                },
                error: function (xhr, status, error) {
                    console.error("Failed to fetch post box cities:", error);
                }
            });
        }


        function initializeLandChangeEvent() {
            $landInput.change(function () {
                showCorrectCountries();
                initializeAutocomplete(postBoxCitiesGlobal, $(this).val());
            });
        }


        function showCorrectCountries() {
            var selectedLand = extractCountryCode($landInput.val());
            $(".country-group").hide(); // Hide all country groups
            if (selectedLand) {
                $(".country-group[data-land='" + selectedLand + "']").show(); // Show the relevant country group
            }
        }
        // Function to initialize the autocomplete fields for PostBox and City
        function initializeAutocomplete(postBoxCities, land) {
            const countryCode = extractCountryCode(land);
            const countryData = postBoxCities[countryCode];

            if (!countryData) {
                console.warn(`No data available for country code: ${countryCode}`);
                return;
            }

            // Generate mapping for postBox to city and vice versa using reduce for efficiency
            const { postBoxToCityMap, cityToPostBoxMap } = countryData.reduce((acc, { postBox, city }) => {
                acc.postBoxToCityMap[postBox] = city;
                acc.cityToPostBoxMap[city] = postBox;
                return acc;
            }, { postBoxToCityMap: {}, cityToPostBoxMap: {} });


            setupPostBoxAutocomplete($postBoxInput, postBoxToCityMap, $cityInput, debounce);
            setupAutocomplete($cityInput, cityToPostBoxMap, $postBoxInput, debounce);
        }


        function extractCountryCode(land) {
            return land.split("-")[0];
        }

        // Setup autocomplete with debounced input
        function setupAutocomplete($input, map, $relatedInput, debounceFn) {
            $input.autocomplete({
                minLength: 0,
                source: debounceFn(function (request, response) {
                    const matcher = new RegExp("^" + $.ui.autocomplete.escapeRegex(request.term), "i");
                    const matches = $.grep(Object.keys(map), item => matcher.test(item));
                    response(matches);
                }, 300), // Debounce delay of 300ms
                select: function (event, ui) {
                    $relatedInput.val(map[ui.item.value]);
                }
            });
        }

        // Setup autocomplete for the postbox input
        function setupPostBoxAutocomplete($postBoxInput, postBoxToCityMap, $relatedInput, debounceFn) {
            const formattedSuggestions = Object.keys(postBoxToCityMap).map(postBox => ({
                label: `${postBox} ${postBoxToCityMap[postBox]}`, // Display as "PostBox City"
                value: postBox // Value to set in the input on selection
            }));

            $postBoxInput.autocomplete({
                minLength: 0,
                source: debounceFn(function(request, response) {
                    const matcher = new RegExp("^" + $.ui.autocomplete.escapeRegex(request.term), "i");
                    const matches = $.grep(formattedSuggestions, suggestion => matcher.test(suggestion.label));
                    response(matches);
                },300), // Debounce delay of 300ms
                select: function (event, ui) {
                    // Set only the postbox value in the input field
                    $postBoxInput.val(ui.item.value);
                    $relatedInput.val(postBoxToCityMap[ui.item.value]);
                    return false; // Prevent default behavior of inserting label
                }
            });
        }

        // Simple debounce function to prevent too many requests in a short period
        function debounce(func, wait) {
            let timeout;
            return function (...args) {
                clearTimeout(timeout);
                timeout = setTimeout(() => func.apply(this, args), wait);
            };
        }

    </script>
}