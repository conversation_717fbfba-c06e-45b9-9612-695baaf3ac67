@model IEnumerable<Admin.TaskDotNet.Dtos.CreditItemDto>
@{
    ViewData["Title"] = "Credits Report";
    Layout = ViewBag.IsPrintView ? "_PrintLayout" : "_Layout";
}

<div class="container mt-4" id="ReportContent">
    <div class="row mb-4">
        <div class="col-12 text-center">
            <h2>@SharedLocalizer["Credits"]</h2>
            @if (ViewBag.DateFrom != null && ViewBag.DateTo != null)
            {
                <h4>
                    @SharedLocalizer["Period"]: 
                    @(((DateTime)ViewBag.DateFrom).ToString("dd.MM.yyyy")) - 
                    @(((DateTime)ViewBag.DateTo).ToString("dd.MM.yyyy"))
                </h4>
            }
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>@SharedLocalizer["Partnername"]</th>
                        <th>@SharedLocalizer["Date"]</th>
                        <th>@SharedLocalizer["PaymentWay"]</th>
                        <th class="text-end">@SharedLocalizer["Amount"] (CHF)</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model.OrderByDescending(m => m.PayDate))
                    {
                        <tr>
                            <td>@item.PartnerName</td>
                            <td>@item.PayDate.ToString("dd.MM.yyyy")</td>
                            <td>@item.PaymentMethod</td>
                            <td class="text-end">@item.Amount.ToString("N2")</td>
                        </tr>
                    }
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="3" class="text-end"><strong>@SharedLocalizer["Total"]:</strong></td>
                        <td class="text-end"><strong>@ViewBag.TotalAmount.ToString("N2")</strong></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>

    @if (ViewBag.IsPrintView)
    {
        <div class="row mt-4 no-print">
            <div class="col-12 text-center">
                <button class="btn btn-primary" onclick="window.print()">
                    <i class="fas fa-print"></i> @SharedLocalizer["Print"]
                </button>
                <a class="btn btn-secondary" href="@Url.Action("Index", "Credits")">
                    <i class="fas fa-arrow-left"></i> @SharedLocalizer["Back"]
                </a>
            </div>
        </div>
    }
    <div class="row mt-4">
        <div class="col-12 text-center">
            <p class="text-muted">
                @SharedLocalizer["Generated on"]: @DateTime.Now.ToString("dd.MM.yyyy HH:mm")
            </p>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Auto-print when page loads
        window.onload = function() {
            window.print();
        };
    </script>
}
