﻿@using Admin.TaskDotNet.Dtos
@model PaginatedListResponse<Activity>
@using Comman.Helper.Extensions
@using TaskDotNet.Helper
@{
    ViewData["Title"] = "GetAllActivity";

    var confirmTitle = @SharedLocalizer["Archive entry"];
    var confirmText = @SharedLocalizer["This request will be deleted after archiving!"];
    var confirmButtonText = @SharedLocalizer["Ok"];
    var cancelButtonText = @SharedLocalizer["Cancel"];
}
<script>
    var confirmTitle = '@confirmTitle';
    var confirmText = '@confirmText';
    var confirmText = '@Html.Raw(confirmText)';
    var confirmButtonText = '@confirmButtonText';
    var cancelButtonText = '@cancelButtonText';
</script>

@section Links {
    <style>

        table td {
            padding: 0 !important;
            color: #000 !important;
        }
    </style>
}

<!-- Content wrapper -->
<div class="content-wrapper">
    <!-- Content -->
    <div class="container-fluid flex-grow-1">
        <h3 class="fw-bold text-white py-3 mb-4">@SharedLocalizer["Activities List"]</h3>

        <!-- Basic Bootstrap Table -->
        <div class="card mb-4">

            
            <div class="text-nowrap m-5 mt-0">
                <div class="row d-flex justify-content-between m-3 align-items-center ">
                    <div class="form-group col-lg-6 row my-2 justify-content-start flex-nowrap">
                        <label for="categoryDropdown" class="col-md-4 col-form-label w-auto fs-5">@SharedLocalizer["Select an activity"]:</label>
                        <div class="col-sm-8">
                            <select class="form-select w-100" asp-items="ViewBag.PartnerActivities" id="categoryDropdown">
                                <option value="0">@SharedLocalizer["View All"]</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <nav aria-label="...">
                            <ul class="pagination justify-content-end" id="pagination-list">
                                @{
                                    int currentPage = Model.PageNumber;
                                    int totalPages = Model.TotalPages;
                                    int pageRange = 3; // Number of pages to show before and after the current page
                                    int startPage = Math.Max(1, currentPage - pageRange);
                                    int endPage = Math.Min(totalPages, currentPage + pageRange);

                                    // Always show the first page
                                    if (startPage > 1)
                                    {
                                        <li class="page-item">
                                            <a class="page-link" href="@Url.Action("GetAllActivities", "Activities", new { PageNumber = 1, SearchTerm = Model.SearchTerm })">1</a>
                                        </li>
                                        if (startPage > 2)
                                        {
                                            <li class="page-item disabled"><span class="page-link">...</span></li>
                                        }
                                    }

                                    // Show the range of pages around the current page
                                    for (int i = startPage; i <= endPage; i++)
                                    {
                                        <li class="page-item @(i == currentPage ? "active" : string.Empty)">
                                            <a class="page-link" href="@Url.Action("GetAllActivities", "Activities", new { PageNumber = i, SearchTerm = Model.SearchTerm })">@i</a>
                                        </li>
                                    }

                                    // Always show the last page
                                    if (endPage < totalPages)
                                    {
                                        if (endPage < totalPages - 1)
                                        {
                                            <li class="page-item disabled"><span class="page-link">...</span></li>
                                        }
                                        <li class="page-item">
                                            <a class="page-link" href="@Url.Action("GetAllActivities", "Activities", new { PageNumber = totalPages, SearchTerm = Model.SearchTerm })">@totalPages</a>
                                        </li>
                                    }
                                }
                            </ul>
                        </nav>
                    </div>
                </div>
                @if (!Model.Data.Any())
                {
                    <div class="row justify-content-center align-items-center px-3 py-6 text-center">
                        <div class="col-lg-6 col-md-8 col-sm-10">
                            <img src="~/images/nathing.png"
                                 alt="No Orders Icon"
                                 class="img-fluid mb-4"
                                 style="width: 160px; height: auto;" />
                            <h3 class="text-muted fw-light mb-3">
                                @SharedLocalizer["EmptyOrdersTitle"]
                            </h3>
                            <p class="text-secondary fs-6">
                                @SharedLocalizer["EmptyOrdersMessage"]
                            </p>
                        </div>
                    </div>
                } 
                <!-- Card Grid -->
                <div class="row row-cols-1 row-cols-md-4 g-4 gy-5">

                    @foreach (var item in Model.Data)
                    {
                        <div class="col-md-6 col-lg-4">
                            <div class="card h-100" style="background-color:#e4e4e4">
                                <div class="card-header bg-gray text-white d-flex justify-content-between align-items-center p-2 fs-5 opacity-75">
                                    <div class="fw-bold">@item.ActivityType.GetDisplayName()</div>
                                    @if (item.ActivityType == ActivityType.Cleaning)
                                    {
                                        <div class="fw-bold">@item.CleaningDate.ToString("dd.MM.yyyy")</div>
                                    }
                                    else
                                    {
                                        <div class="fw-bold">@item.MovingDate.ToString("dd.MM.yyyy")</div>
                                    }
                                </div>
                                <div class="card-body p-4">
                                    <!-- Activity Details -->
                                    @if (item.ActivityType == ActivityType.Moving || item.ActivityType == ActivityType.MovingAndCleaning)
                                    {
                                        <table class="table table-borderless">
                                            <tbody>
                                                <tr>
                                                    <td><strong>@SharedLocalizer["From"]:</strong></td>
                                                    <td>@(item.PostBox) @(item.City)</td>
                                                </tr>


                                                <tr>
                                                    <td><strong>@SharedLocalizer["To"]:</strong></td>
                                                    <td>@(item.TPostBox) @(item.TCity)</td>
                                                </tr>

                                                <tr style="height: 20px;"></tr>

                                                <tr>
                                                    <td style=" vertical-align: top; "><strong>@SharedLocalizer["Auszug"]:</strong></td>
                                                    <td>
                                                        @(item.Room) @SharedLocalizer["Room"]-@(SharedLocalizer[item.Object])<br />
                                                        @(SharedLocalizer[item.Floor]), @SharedLocalizer["Lift"]: @(item.FLift ?? false ? @SharedLocalizer["Yes"] : @SharedLocalizer["No"])
                                                    </td>
                                                </tr>

                                                <tr>
                                                    <td><strong>@SharedLocalizer["Einzug"]:</strong></td>
                                                    <td>
                                                        @(SharedLocalizer[item.TFloor]), @SharedLocalizer["Lift"]: @(item.TLift ?? false ? @SharedLocalizer["Yes"] : @SharedLocalizer["No"])
                                                    </td>
                                                </tr>
                                                <tr style="height: 10px;"></tr>

                                                <tr>
                                                    <td><strong>@SharedLocalizer["Flexibility"]:</strong></td>
                                                    <td>@(SharedLocalizer[item.Flexible])</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                        @if (item.ActivityType == ActivityType.MovingAndCleaning)
                                        {
                                            <hr class="m-2" />
                                            <table class="table table-borderless">
                                                <tbody>
                                                    <tr>
                                                        <td><strong>@SharedLocalizer["CleaningDate"]:</strong></td>
                                                        <td>@item.CleaningDate.ToString("dd.MM.yyyy")</td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>@SharedLocalizer["HandOverDate"]:</strong></td>
                                                        <td>@item.HandOverDate.ToString("dd.MM.yyyy")</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        }

                                    }
                                    else if (item.ActivityType == ActivityType.Cleaning)
                                    {

                                        <table class="table table-borderless">
                                            <tbody>
                                                <tr>
                                                    <td><strong>@SharedLocalizer["Type"]:</strong></td>
                                                    <td>@(SharedLocalizer[item.CleaningType])</td>
                                                </tr>
                                                <tr style="height: 10px;"></tr>

                                                <tr>
                                                    <td style=" vertical-align: top; "><strong>@SharedLocalizer["Ort"]:</strong></td>
                                                    <td>
                                                        @(item.PostBox) @(item.City) <br />
                                                        @(item.Room) @SharedLocalizer["Room"]-@(SharedLocalizer[item.Object])
                                                    </td>
                                                </tr>

                                                <tr>
                                                    <td><strong>@SharedLocalizer["Area"]:</strong></td>
                                                    <td>
                                                        @(item.Area), @(SharedLocalizer[item.Floor])
                                                    </td>
                                                </tr>
                                                <tr style="height: 4px;"></tr>
                                                <tr>
                                                    <td><strong>@SharedLocalizer["Flexibility"]:</strong></td>
                                                    <td>@(SharedLocalizer[item.Flexible])</td>
                                                </tr>
                                                <tr style="height: 4px;"></tr>
                                                <tr>
                                                    <td><strong>@SharedLocalizer["HandOverDate"]:</strong></td>
                                                    <td>@item.HandOverDate.ToString("dd.MM.yyyy")</td>
                                                </tr>
                                            </tbody>
                                        </table>

                                    }
                                    else if (item.ActivityType == ActivityType.PaintingAndGisper)
                                    {
                                        <table class="table table-borderless">
                                            <tbody>

                                                <tr>
                                                    <td style=" vertical-align: top; "><strong>@SharedLocalizer["Ort"]:</strong></td>
                                                    <td>
                                                        @(item.PostBox) @(item.City) <br />
                                                        @(item.Room) @SharedLocalizer["Room"]-@(SharedLocalizer[item.Object]) <br />
                                                        @(SharedLocalizer[item.Floor])
                                                    </td>
                                                </tr>
                                                <tr style="height: 4px;"></tr>
                                                <tr>
                                                    <td><strong>@SharedLocalizer["Area"]:</strong></td>
                                                    <td>
                                                        @(item.Area)
                                                    </td>
                                                </tr>
                                                <tr style="height: 4px;"></tr>
                                                <tr>
                                                    <td><strong>@SharedLocalizer["Workspace"]:</strong></td>
                                                    <td>@(SharedLocalizer[item.Workspace])</td>
                                                </tr>

                                                <tr style="height: 15px;"></tr>

                                                <tr>
                                                    <td><strong>@SharedLocalizer["Heater"]:</strong></td>
                                                    <td>@(item.Heater)</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>@SharedLocalizer["Walls"]:</strong></td>
                                                    <td>@(item.Walls)</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>@SharedLocalizer["Doors"]:</strong></td>
                                                    <td>@(item.Doors)</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>@SharedLocalizer["Windows"]:</strong></td>
                                                    <td>@(item.Windows)</td>
                                                </tr>
                                            </tbody>
                                        </table>

                                    }
                                    else
                                    {
                                        <table class="table table-borderless">
                                            <tbody>
                                                <tr>
                                                    <td><strong>@SharedLocalizer["Ort"]:</strong></td>
                                                    <td>@(item.PostBox) @(item.City)</td>
                                                </tr>
                                                <tr style="height: 4px;"></tr>

                                                <tr>
                                                    <td><strong>@SharedLocalizer["Object"]:</strong></td>
                                                    <td>@(SharedLocalizer[item.Object])</td>
                                                </tr>
                                                <tr style="height: 4px;"></tr>

                                                <tr>
                                                    <td><strong>@SharedLocalizer["MoreWork"]:</strong></td>
                                                    <td>@(item.MoreWork)</td>
                                                </tr>
                                                <tr style="height: 4px;"></tr>

                                                <tr>
                                                    <td><strong>@SharedLocalizer["Focus"]:</strong></td>
                                                    <td>
                                                        @(SharedLocalizer[item.Focus])
                                                    </td>
                                                </tr>
                                                <tr style="height: 4px;"></tr>

                                                <tr>
                                                    <td><strong>@SharedLocalizer["Flexibility"]:</strong></td>
                                                    <td>@(SharedLocalizer[item.Flexible])</td>
                                                </tr>
                                                <tr style="height: 15px;"></tr>
                                                <tr>
                                                    <td><strong>@SharedLocalizer["Notes"]:</strong></td>
                                                </tr>
                                                <tr>
                                                    <td class="text-wrap" colspan="2">@(item.Notes)</td>
                                                </tr>
                                            </tbody>
                                        </table>

                                    }
                                </div>
                                <div class="card-footer bg-transparent p-0">
                                    @if (item.ActivityType == ActivityType.Moving || item.ActivityType == ActivityType.MovingAndCleaning)
                                    {
                                        <div class="form-check form-switch m-0 m-3 d-flex align-items-center justify-content-end">
                                            <label class="form-check-label">@SharedLocalizer["HasInventory"]? </label>
                                            <input class="form-check-input m-0 ms-3" type="checkbox" onclick="return false;" @(item.Inventar ?? false ? "checked" : "")
                                                   style="height:25px; width:60px;" />
                                        </div>
                                    }

                                    <p class="px-2 m-0 h5 text-white" style="background-color:#0a6d68;">CHF @(item.Preis.ToString("0.00"))</p>

                                    <div class="d-flex justify-content-between align-items-center m-3">

                                        <a type="button" class="btn me-5 btn-primary" asp-controller="Activities" asp-action="ActivityDetails" asp-route-id="@item.Id">@SharedLocalizer["View order"]</a>
                                        @if (User.IsInRole("Admin"))
                                        {
                                            <button class="btn btn-danger archive-btn" data-url="@Url.Action("AddToArchive", "Archive", new { id = item.Id })">
                                                @SharedLocalizer["Add to archive"]
                                            </button>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>


        </div>
        <!--/ Basic Bootstrap Table -->
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>

        // SweetAlert2 confirmation for archive buttons
        document.querySelectorAll('.archive-btn').forEach(button => {
            button.addEventListener('click', function () {
                var url = this.getAttribute('data-url');
                Swal.fire({
                    title: confirmTitle,
                    text: confirmText,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: confirmButtonText,
                    cancelButtonText: cancelButtonText
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Redirect to the archive action URL
                        window.location.href = url;
                    }
                });
            });
        });

          $(document).ready(function () {

            const searchTerm = "@Model.SearchTerm";

            if (searchTerm) {
                $("#categoryDropdown").val(decodeURIComponent(searchTerm));
            }

            $("#categoryDropdown").on("change", function () {
                const selectedValue = $(this).val();
                // Construct the URL with proper encoding for the query parameter
                const redirectUrl = `/Activities/GetAllActivities?SearchTerm=${encodeURIComponent(selectedValue)}`;
                window.location.href = redirectUrl;
            });
        });
    </script>
}
