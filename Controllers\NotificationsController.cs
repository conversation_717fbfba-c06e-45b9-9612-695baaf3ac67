﻿using Admin.TaskDotNet.Dtos;
using Comman.Helper.Extensions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TaskDotNet.Comman.DataAccess;
using TaskDotNet.Helper;
using TaskDotNet.Models;

namespace Admin.TaskDotNet.Controllers;

public class NotificationsController : Controller
{
    private readonly ApplicationDbContext _context;

    public NotificationsController(ApplicationDbContext context)
    {
        _context = context;
    }

    [Authorize(Roles = "Admin,Moderator")]
    public async Task<IActionResult> LoadNotifications()
    {
        var notifications = await _context.ActivityNotifications.ToListAsync();

        var notificationsDto = notifications.Select(m => new ActivityNotificationDto
        { 
            ActivityType = m.ActivityType.GetDisplayName(),
            City = m.City,
            ExcuteDate = m.ExcuteDate.ToString("dd.MM.yyyy"),
            Name = m.Name,
            PostBox = m.PostBox,
            Salute = m.Salute.GetDisplayName() 
        });

         _context.ActivityNotifications.RemoveRange(notifications);
        await _context.SaveChangesAsync();

        return Json(notificationsDto);
    }
}
