﻿@model Admin.TaskDotNet.Dtos.AdvertisingEmailDto
@{
    ViewData["Title"] = "Advertising Send Email";
    var currentLangeCode = System.Globalization.CultureInfo.CurrentCulture.TwoLetterISOLanguageName;
}

@section Links {
    <style>

        .cke_notifications_area {
            display: none;
        }
    </style>
}

<!-- Overlay -->
<div class="content-wrapper">
    <!-- Content -->

    <div class="container-fluid flex-grow-1 ">
        <h3 class="fw-bold text-white py-3 mb-4">@SharedLocalizer["EmailSenden"]</h3>

        <div class="row">
            <div class="col-md-12">

                <div class="card mb-4">
                    <!-- Account -->

                    <hr class="my-0" />
                    <div class="card-body">
                        <div class="row justify-content-md-between m-3">
                            <div class="form-group col-6 row my-2 justify-content-start">
                                <label class="col-md-4 col-form-label fs-5" for="EmailText">@SharedLocalizer["Choose email content"]:</label>
                                <div class="col-sm-8">
                                    <select id="EmailText" name="EmailText" class=" form-select" asp-items="ViewBag.EmailTexts"><option disabled selected>@SharedLocalizer["Select Template"]</option></select>
                                </div>
                                <span asp-validation-for="Title" class="text-danger"></span>
                            </div>
                            <div class="form-group col-6 row my-2 justify-content-md-end">
                                <label class="col-md-4 col-form-label text-md-end fs-5" for="Branch">@SharedLocalizer["To which branch"]:</label>
                                <div class="col-sm-8">
                                    <select id="AllBranchs" name="AllBranchs" class=" form-select" asp-items="ViewBag.Branchs">
                                        <option disabled selected>@SharedLocalizer["Select Branch"]</option>
                                        <option value="all">@SharedLocalizer["All"]</option>
                                        </select>
                                </div>
                                <span asp-validation-for="Branch" class="text-danger"></span>
                            </div>
                        </div>
                        <form class="m-3" id="myForm" asp-action="SendEmail" enctype="multipart/form-data">
                            <div class="row">
                                <input type="hidden" asp-for="Title" />
                                <input type="hidden" asp-for="Branch" />
                                <div class="mb-3 col-md-12">
                                    <textarea asp-for="Message" id="editor"></textarea>
                                </div>
                                <span asp-validation-for="Message" class="text-danger"></span>

                            </div>
                            <div class="mt-2">
                                <input type="submit" class="btn btn-primary me-2" value="@SharedLocalizer["Send"]" />
                                <a type="reset" class="btn btn-outline-secondary" asp-controller="Advertising" asp-action="Index">@SharedLocalizer["Cancel"]</a>
                            </div>
                        </form>
                    </div>
                    <!-- /Account -->
                </div>

            </div>
        </div>
    </div>
    <!-- / Content -->
</div>


@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script src="https://cdn.ckeditor.com/4.22.1/full/ckeditor.js"></script>
    <script>
        $(document).ready(function () {
            // Initialize CKEditor
            CKEDITOR.replace('editor', {
                height: 400,
                language: '@currentLangeCode'
            });

            $("#AllBranchs").change(function () {
                $("#Branch").val($(this).val());
            });

            $("#EmailText").change(function () {
                var id = $(this).val();
                $.ajax({
                    url: '/EmailText/GetText/' + id,
                    type: 'GET',
                    success: function (data) {
                        // Set the fetched content in the CKEditor
                        CKEDITOR.instances.editor.setData(data.content);
                        $("#Title").val(data.title);
                    },
                    error: function () {
                        alert('Error occurred while fetching text.');
                    }
                });
            });
        });
    </script>
}


