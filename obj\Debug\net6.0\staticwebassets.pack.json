{"Files": [{"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\obj\\Debug\\net6.0\\scopedcss\\projectbundle\\Admin.TaskDotNet.bundle.scp.css", "PackagePath": "staticwebassets\\Admin.TaskDotNet.2ptiq1ljdf.bundle.scp.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\.browserslistrc", "PackagePath": "staticwebassets\\Dashboard\\.browserslistrc"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\.eslintignore", "PackagePath": "staticwebassets\\Dashboard\\.eslintignore"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\.eslintrc.json", "PackagePath": "staticwebassets\\Dashboard\\.eslintrc.json"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\.gitignore", "PackagePath": "staticwebassets\\Dashboard\\.gitignore"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\.prettierignore", "PackagePath": "staticwebassets\\Dashboard\\.prettierignore"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\.prettierrc.json", "PackagePath": "staticwebassets\\Dashboard\\.prettierrc.json"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\CHANGELOG.md", "PackagePath": "staticwebassets\\Dashboard\\CHANGELOG.md"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\LICENSE.md", "PackagePath": "staticwebassets\\Dashboard\\LICENSE.md"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\README.md", "PackagePath": "staticwebassets\\Dashboard\\README.md"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\css\\demo.css", "PackagePath": "staticwebassets\\Dashboard\\assets\\css\\demo.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\AGB.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\AGB.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\ActivateEmail.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\ActivateEmail.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Admin.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\Admin.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Attention-removebg-preview.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\Attention-removebg-preview.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Dashboard1.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\Dashboard1.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Dashboard2.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\Dashboard2.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\LOGO.svg", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\LOGO.svg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Login.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\Login.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Post_Code.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\Post_Code.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Post_Konto.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\Post_Konto.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Schloss.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\Schloss.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Twint_Konto.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\Twint_Konto.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\Visa_Konto.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\Visa_Konto.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\avatars\\1.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\avatars\\1.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\avatars\\5.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\avatars\\5.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\avatars\\6.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\avatars\\6.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\avatars\\7.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\avatars\\7.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\backgrounds\\18.jpg", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\backgrounds\\18.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\cleaning.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\cleaning.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\de-flag.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\de-flag.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\1.jpg", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\elements\\1.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\11.jpg", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\elements\\11.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\12.jpg", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\elements\\12.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\13.jpg", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\elements\\13.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\17.jpg", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\elements\\17.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\18.jpg", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\elements\\18.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\19.jpg", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\elements\\19.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\2.jpg", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\elements\\2.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\20.jpg", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\elements\\20.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\3.jpg", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\elements\\3.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\4.jpg", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\elements\\4.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\5.jpg", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\elements\\5.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\elements\\7.jpg", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\elements\\7.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\favicon\\favicon.ico", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\favicon\\favicon.ico"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\fr-flag.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\fr-flag.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\gisper.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\gisper.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\iconfacebook.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\iconfacebook.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\iconinsta.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\iconinsta.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\iconlinked.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\iconlinked.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\brands\\asana.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\brands\\asana.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\brands\\behance.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\brands\\behance.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\brands\\dribbble.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\brands\\dribbble.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\brands\\facebook.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\brands\\facebook.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\brands\\github.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\brands\\github.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\brands\\google.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\brands\\google.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\brands\\instagram.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\brands\\instagram.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\brands\\mailchimp.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\brands\\mailchimp.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\brands\\slack.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\brands\\slack.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\brands\\twitter.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\brands\\twitter.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\unicons\\cc-primary.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\unicons\\cc-primary.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\unicons\\cc-success.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\unicons\\cc-success.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\unicons\\cc-warning.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\unicons\\cc-warning.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\unicons\\chart-success.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\unicons\\chart-success.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\unicons\\chart.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\unicons\\chart.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\unicons\\paypal.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\unicons\\paypal.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\unicons\\wallet-info.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\unicons\\wallet-info.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icons\\unicons\\wallet.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icons\\unicons\\wallet.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\icontwitter.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\icontwitter.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\illustrations\\girl-doing-yoga-light.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\illustrations\\girl-doing-yoga-light.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\illustrations\\man-with-laptop-light.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\illustrations\\man-with-laptop-light.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\illustrations\\page-misc-error-light.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\illustrations\\page-misc-error-light.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\it-flag.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\it-flag.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\language.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\language.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\layouts\\layout-container-light.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\layouts\\layout-container-light.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\layouts\\layout-fluid-light.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\layouts\\layout-fluid-light.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\layouts\\layout-without-menu-light.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\layouts\\layout-without-menu-light.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\layouts\\layout-without-navbar-light.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\layouts\\layout-without-navbar-light.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\lock.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\lock.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\login2.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\login2.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\logo.jpg", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\logo.jpg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\logo.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\logo.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\moving and cleaning.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\moving and cleaning.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\moving.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\moving.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\painting.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\painting.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\pizaa.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\pizaa.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\thankyou.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\thankyou.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\img\\us-flag.png", "PackagePath": "staticwebassets\\Dashboard\\assets\\img\\us-flag.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\js\\config.js", "PackagePath": "staticwebassets\\Dashboard\\assets\\js\\config.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\js\\dashboards-analytics.js", "PackagePath": "staticwebassets\\Dashboard\\assets\\js\\dashboards-analytics.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\js\\extended-ui-perfect-scrollbar.js", "PackagePath": "staticwebassets\\Dashboard\\assets\\js\\extended-ui-perfect-scrollbar.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\js\\form-basic-inputs.js", "PackagePath": "staticwebassets\\Dashboard\\assets\\js\\form-basic-inputs.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\js\\main.js", "PackagePath": "staticwebassets\\Dashboard\\assets\\js\\main.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\js\\pages-account-settings-account.js", "PackagePath": "staticwebassets\\Dashboard\\assets\\js\\pages-account-settings-account.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\js\\ui-modals.js", "PackagePath": "staticwebassets\\Dashboard\\assets\\js\\ui-modals.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\js\\ui-popover.js", "PackagePath": "staticwebassets\\Dashboard\\assets\\js\\ui-popover.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\js\\ui-toasts.js", "PackagePath": "staticwebassets\\Dashboard\\assets\\js\\ui-toasts.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\notification.wav", "PackagePath": "staticwebassets\\Dashboard\\assets\\notification.wav"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\css\\core.css", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\css\\core.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\css\\pages\\page-account-settings.css", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\css\\pages\\page-account-settings.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\css\\pages\\page-auth.css", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\css\\pages\\page-auth.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\css\\pages\\page-icons.css", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\css\\pages\\page-icons.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\css\\pages\\page-misc.css", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\css\\pages\\page-misc.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\css\\theme-default.css", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\css\\theme-default.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\fonts\\boxicons.css", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\fonts\\boxicons.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\fonts\\boxicons\\boxicons.eot", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\fonts\\boxicons\\boxicons.eot"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\fonts\\boxicons\\boxicons.svg", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\fonts\\boxicons\\boxicons.svg"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\fonts\\boxicons\\boxicons.ttf", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\fonts\\boxicons\\boxicons.ttf"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\fonts\\boxicons\\boxicons.woff", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\fonts\\boxicons\\boxicons.woff"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\fonts\\boxicons\\boxicons.woff2", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\fonts\\boxicons\\boxicons.woff2"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\js\\bootstrap.js", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\js\\bootstrap.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\js\\helpers.js", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\js\\helpers.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\js\\menu.js", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\js\\menu.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\libs\\apex-charts\\apex-charts.css", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\libs\\apex-charts\\apex-charts.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\libs\\apex-charts\\apexcharts.js", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\libs\\apex-charts\\apexcharts.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\libs\\highlight\\highlight-github.css", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\libs\\highlight\\highlight-github.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\libs\\highlight\\highlight.css", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\libs\\highlight\\highlight.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\libs\\highlight\\highlight.js", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\libs\\highlight\\highlight.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\libs\\jquery\\jquery.js", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\libs\\jquery\\jquery.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\libs\\masonry\\masonry.js", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\libs\\masonry\\masonry.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\libs\\perfect-scrollbar\\perfect-scrollbar.css", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\libs\\perfect-scrollbar\\perfect-scrollbar.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\libs\\perfect-scrollbar\\perfect-scrollbar.js", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\libs\\perfect-scrollbar\\perfect-scrollbar.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\assets\\vendor\\libs\\popper\\popper.js", "PackagePath": "staticwebassets\\Dashboard\\assets\\vendor\\libs\\popper\\popper.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\build-config.js", "PackagePath": "staticwebassets\\Dashboard\\build-config.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\fonts\\boxicons.scss", "PackagePath": "staticwebassets\\Dashboard\\fonts\\boxicons.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\gulpfile.js", "PackagePath": "staticwebassets\\Dashboard\\gulpfile.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\js\\bootstrap.js", "PackagePath": "staticwebassets\\Dashboard\\js\\bootstrap.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\js\\helpers.js", "PackagePath": "staticwebassets\\Dashboard\\js\\helpers.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\js\\menu.js", "PackagePath": "staticwebassets\\Dashboard\\js\\menu.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\libs\\apex-charts\\apex-charts.scss", "PackagePath": "staticwebassets\\Dashboard\\libs\\apex-charts\\apex-charts.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\libs\\apex-charts\\apexcharts.js", "PackagePath": "staticwebassets\\Dashboard\\libs\\apex-charts\\apexcharts.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\libs\\highlight\\highlight-github.scss", "PackagePath": "staticwebassets\\Dashboard\\libs\\highlight\\highlight-github.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\libs\\highlight\\highlight.js", "PackagePath": "staticwebassets\\Dashboard\\libs\\highlight\\highlight.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\libs\\highlight\\highlight.scss", "PackagePath": "staticwebassets\\Dashboard\\libs\\highlight\\highlight.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\libs\\jquery\\jquery.js", "PackagePath": "staticwebassets\\Dashboard\\libs\\jquery\\jquery.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\libs\\masonry\\masonry.js", "PackagePath": "staticwebassets\\Dashboard\\libs\\masonry\\masonry.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\libs\\perfect-scrollbar\\perfect-scrollbar.js", "PackagePath": "staticwebassets\\Dashboard\\libs\\perfect-scrollbar\\perfect-scrollbar.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\libs\\perfect-scrollbar\\perfect-scrollbar.scss", "PackagePath": "staticwebassets\\Dashboard\\libs\\perfect-scrollbar\\perfect-scrollbar.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\libs\\popper\\popper.js", "PackagePath": "staticwebassets\\Dashboard\\libs\\popper\\popper.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\package-lock.json", "PackagePath": "staticwebassets\\Dashboard\\package-lock.json"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\package.json", "PackagePath": "staticwebassets\\Dashboard\\package.json"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\_accordion.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\_accordion.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\_alert.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\_alert.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\_badge.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\_badge.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\_breadcrumb.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\_breadcrumb.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\_button-group.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\_button-group.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\_buttons.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\_buttons.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\_card.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\_card.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\_carousel.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\_carousel.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\_close.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\_close.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\_dropdown.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\_dropdown.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\_forms.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\_forms.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\_functions.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\_functions.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\_include.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\_include.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\_list-group.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\_list-group.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\_mixins.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\_mixins.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\_modal.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\_modal.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\_nav.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\_nav.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\_navbar.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\_navbar.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\_offcanvas.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\_offcanvas.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\_pagination.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\_pagination.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\_popover.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\_popover.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\_progress.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\_progress.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\_reboot.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\_reboot.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\_root.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\_root.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\_spinners.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\_spinners.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\_tables.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\_tables.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\_toasts.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\_toasts.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\_tooltip.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\_tooltip.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\_type.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\_type.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\_utilities-ltr.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\_utilities-ltr.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\_utilities.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\_utilities.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\_variables.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\_variables.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\forms\\_floating-labels.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\forms\\_floating-labels.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\forms\\_form-control.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\forms\\_form-control.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\forms\\_form-range.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\forms\\_form-range.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\forms\\_form-select.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\forms\\_form-select.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\forms\\_input-group.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\forms\\_input-group.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\forms\\_labels.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\forms\\_labels.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\forms\\_validation.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\forms\\_validation.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\mixins\\_alert.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\mixins\\_alert.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\mixins\\_badge.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\mixins\\_badge.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\mixins\\_buttons.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\mixins\\_buttons.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\mixins\\_caret.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\mixins\\_caret.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\mixins\\_dropdown.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\mixins\\_dropdown.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\mixins\\_forms.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\mixins\\_forms.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\mixins\\_list-group.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\mixins\\_list-group.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\mixins\\_misc.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\mixins\\_misc.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\mixins\\_navs.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\mixins\\_navs.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\mixins\\_pagination.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\mixins\\_pagination.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\mixins\\_progress.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\mixins\\_progress.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\mixins\\_table-variants.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\mixins\\_table-variants.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap-extended\\mixins\\_toasts.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap-extended\\mixins\\_toasts.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_bootstrap.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_bootstrap.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_colors.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_colors.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_components.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_components.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_components\\_app-brand.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_components\\_app-brand.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_components\\_avatar.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_components\\_avatar.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_components\\_base.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_components\\_base.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_components\\_common.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_components\\_common.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_components\\_footer.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_components\\_footer.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_components\\_include.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_components\\_include.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_components\\_layout.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_components\\_layout.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_components\\_menu.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_components\\_menu.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_components\\_mixins.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_components\\_mixins.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_components\\_text-divider.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_components\\_text-divider.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_components\\_variables.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_components\\_variables.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_components\\mixins\\_app-brand.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_components\\mixins\\_app-brand.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_components\\mixins\\_avatar.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_components\\mixins\\_avatar.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_components\\mixins\\_footer.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_components\\mixins\\_footer.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_components\\mixins\\_menu.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_components\\mixins\\_menu.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_components\\mixins\\_misc.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_components\\mixins\\_misc.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_components\\mixins\\_navbar.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_components\\mixins\\_navbar.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_components\\mixins\\_text-divider.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_components\\mixins\\_text-divider.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_custom-variables\\_bootstrap-extended.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_custom-variables\\_bootstrap-extended.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_custom-variables\\_components.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_custom-variables\\_components.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_custom-variables\\_libs.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_custom-variables\\_libs.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_custom-variables\\_pages.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_custom-variables\\_pages.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_custom-variables\\_support.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_custom-variables\\_support.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_theme\\_common.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_theme\\_common.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\_theme\\_theme.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\_theme\\_theme.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\core.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\core.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\pages\\page-account-settings.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\pages\\page-account-settings.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\pages\\page-auth.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\pages\\page-auth.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\pages\\page-icons.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\pages\\page-icons.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\pages\\page-misc.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\pages\\page-misc.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\scss\\theme-default.scss", "PackagePath": "staticwebassets\\Dashboard\\scss\\theme-default.scss"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\tasks\\build.js", "PackagePath": "staticwebassets\\Dashboard\\tasks\\build.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\tasks\\prod.js", "PackagePath": "staticwebassets\\Dashboard\\tasks\\prod.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\webpack.config.js", "PackagePath": "staticwebassets\\Dashboard\\webpack.config.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Dashboard\\yarn.lock", "PackagePath": "staticwebassets\\Dashboard\\yarn.lock"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Armchair1 with armrests.png", "PackagePath": "staticwebassets\\Images\\Armchair1 with armrests.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Armchair1 without armrests.png", "PackagePath": "staticwebassets\\Images\\Armchair1 without armrests.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Armchair6 with armrests.png", "PackagePath": "staticwebassets\\Images\\Armchair6 with armrests.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Armchair6 without armrests.png", "PackagePath": "staticwebassets\\Images\\Armchair6 without armrests.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Autoreifen.png", "PackagePath": "staticwebassets\\Images\\Autoreifen.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Bar.png", "PackagePath": "staticwebassets\\Images\\Bar.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Bed complete.png", "PackagePath": "staticwebassets\\Images\\Bed complete.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Bedding1.png", "PackagePath": "staticwebassets\\Images\\Bedding1.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Bedding2.png", "PackagePath": "staticwebassets\\Images\\Bedding2.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Bedside table.png", "PackagePath": "staticwebassets\\Images\\Bedside table.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Bedside table2.png", "PackagePath": "staticwebassets\\Images\\Bedside table2.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Book.png", "PackagePath": "staticwebassets\\Images\\Book.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Bookshelf, can be dismantled.png", "PackagePath": "staticwebassets\\Images\\Bookshelf, can be dismantled.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Bookshelf.png", "PackagePath": "staticwebassets\\Images\\Bookshelf.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Broom cupboard.png", "PackagePath": "staticwebassets\\Images\\Broom cupboard.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Buffet (without attachment).png", "PackagePath": "staticwebassets\\Images\\Buffet (without attachment).png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Buffet with essays.png", "PackagePath": "staticwebassets\\Images\\Buffet with essays.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Buffet, with attachment.png", "PackagePath": "staticwebassets\\Images\\Buffet, with attachment.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Bunk bed, complete.png", "PackagePath": "staticwebassets\\Images\\Bunk bed, complete.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Cabinet with sliding doors-drawers.png", "PackagePath": "staticwebassets\\Images\\Cabinet with sliding doors-drawers.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Carpet1.png", "PackagePath": "staticwebassets\\Images\\Carpet1.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Carpet2.png", "PackagePath": "staticwebassets\\Images\\Carpet2.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Carpet5.png", "PackagePath": "staticwebassets\\Images\\Carpet5.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Carpet7.png", "PackagePath": "staticwebassets\\Images\\Carpet7.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Ceiling lamp1.png", "PackagePath": "staticwebassets\\Images\\Ceiling lamp1.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Ceiling lamp2.png", "PackagePath": "staticwebassets\\Images\\Ceiling lamp2.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Ceiling lamp3.png", "PackagePath": "staticwebassets\\Images\\Ceiling lamp3.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Ceiling lamp4.png", "PackagePath": "staticwebassets\\Images\\Ceiling lamp4.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Ceiling lamp5.png", "PackagePath": "staticwebassets\\Images\\Ceiling lamp5.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Chair with armrests.png", "PackagePath": "staticwebassets\\Images\\Chair with armrests.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Chair without armrests.png", "PackagePath": "staticwebassets\\Images\\Chair without armrests.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Chair2.png", "PackagePath": "staticwebassets\\Images\\Chair2.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Chandelier.png", "PackagePath": "staticwebassets\\Images\\Chandelier.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Clothes.png", "PackagePath": "staticwebassets\\Images\\Clothes.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Collapsible shelf.png", "PackagePath": "staticwebassets\\Images\\Collapsible shelf.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Computer.png", "PackagePath": "staticwebassets\\Images\\Computer.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Corner bench (per seat).png", "PackagePath": "staticwebassets\\Images\\Corner bench (per seat).png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Corner bench.png", "PackagePath": "staticwebassets\\Images\\Corner bench.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Couchtisch.png", "PackagePath": "staticwebassets\\Images\\Couchtisch.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Cupboard, up to 2 doors, cannot be dismantled.png", "PackagePath": "staticwebassets\\Images\\Cupboard, up to 2 doors, cannot be dismantled.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Desk.png", "PackagePath": "staticwebassets\\Images\\Desk.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Dishwasher.png", "PackagePath": "staticwebassets\\Images\\Dishwasher.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Dismountable cabinet.png", "PackagePath": "staticwebassets\\Images\\Dismountable cabinet.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Dresser with mirror.png", "PackagePath": "staticwebassets\\Images\\Dresser with mirror.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Dresser without mirror.png", "PackagePath": "staticwebassets\\Images\\Dresser without mirror.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Extension wall (per meter).png", "PackagePath": "staticwebassets\\Images\\Extension wall (per meter).png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Extension wall.png", "PackagePath": "staticwebassets\\Images\\Extension wall.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Fahrrad.png", "PackagePath": "staticwebassets\\Images\\Fahrrad.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\File Cabinet.png", "PackagePath": "staticwebassets\\Images\\File Cabinet.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Floor1 lamp.png", "PackagePath": "staticwebassets\\Images\\Floor1 lamp.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Flowerpot.png", "PackagePath": "staticwebassets\\Images\\Flowerpot.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\French bed.png", "PackagePath": "staticwebassets\\Images\\French bed.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Fridge - chest up to 120 liters.png", "PackagePath": "staticwebassets\\Images\\Fridge - chest up to 120 liters.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Fridge - chest.png", "PackagePath": "staticwebassets\\Images\\Fridge - chest.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Garderobe.png", "PackagePath": "staticwebassets\\Images\\Garderobe.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Gartenmöbel.png", "PackagePath": "staticwebassets\\Images\\Gartenmöbel.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Grandfather clock.png", "PackagePath": "staticwebassets\\Images\\Grandfather clock.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Grill.png", "PackagePath": "staticwebassets\\Images\\Grill.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Home organ.png", "PackagePath": "staticwebassets\\Images\\Home organ.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Ironing board.png", "PackagePath": "staticwebassets\\Images\\Ironing board.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\KInderwagen.png", "PackagePath": "staticwebassets\\Images\\KInderwagen.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Kinderrad.png", "PackagePath": "staticwebassets\\Images\\Kinderrad.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Klappstuhl.png", "PackagePath": "staticwebassets\\Images\\Klappstuhl.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Klapptisch.png", "PackagePath": "staticwebassets\\Images\\Klapptisch.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Koffer.png", "PackagePath": "staticwebassets\\Images\\Koffer.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Kommode.png", "PackagePath": "staticwebassets\\Images\\Kommode.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Leiter.png", "PackagePath": "staticwebassets\\Images\\Leiter.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Microwave.png", "PackagePath": "staticwebassets\\Images\\Microwave.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Mirror, over 0.8 m.png", "PackagePath": "staticwebassets\\Images\\Mirror, over 0.8 m.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Moving.png", "PackagePath": "staticwebassets\\Images\\Moving.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Mülltonne.png", "PackagePath": "staticwebassets\\Images\\Mülltonne.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Nathing.png", "PackagePath": "staticwebassets\\Images\\Nathing.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Non-dismountable wardrobe.png", "PackagePath": "staticwebassets\\Images\\Non-dismountable wardrobe.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Outdoorsofa.png", "PackagePath": "staticwebassets\\Images\\Outdoorsofa.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Piano.png", "PackagePath": "staticwebassets\\Images\\Piano.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Pictures.png", "PackagePath": "staticwebassets\\Images\\Pictures.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Plafoniera.png", "PackagePath": "staticwebassets\\Images\\Plafoniera.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Plant1.png", "PackagePath": "staticwebassets\\Images\\Plant1.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Plant2.png", "PackagePath": "staticwebassets\\Images\\Plant2.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Plant5.png", "PackagePath": "staticwebassets\\Images\\Plant5.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Plant7.png", "PackagePath": "staticwebassets\\Images\\Plant7.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Plantk.png", "PackagePath": "staticwebassets\\Images\\Plantk.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Playpen.png", "PackagePath": "staticwebassets\\Images\\Playpen.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Printer.png", "PackagePath": "staticwebassets\\Images\\Printer.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Rasenmäher.png", "PackagePath": "staticwebassets\\Images\\Rasenmäher.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Schlitten.png", "PackagePath": "staticwebassets\\Images\\Schlitten.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Schubkarre.png", "PackagePath": "staticwebassets\\Images\\Schubkarre.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Schuhschrank.png", "PackagePath": "staticwebassets\\Images\\Schuhschrank.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Seating landscape (per seat).png", "PackagePath": "staticwebassets\\Images\\Seating landscape (per seat).png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Sewing machine (closet).png", "PackagePath": "staticwebassets\\Images\\Sewing machine (closet).png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Showcase (glass cabine).png", "PackagePath": "staticwebassets\\Images\\Showcase (glass cabine).png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Sideboard2.png", "PackagePath": "staticwebassets\\Images\\Sideboard2.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Sideboard5.png", "PackagePath": "staticwebassets\\Images\\Sideboard5.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Sideboard7.png", "PackagePath": "staticwebassets\\Images\\Sideboard7.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Single bed.png", "PackagePath": "staticwebassets\\Images\\Single bed.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Ski.png", "PackagePath": "staticwebassets\\Images\\Ski.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Sofa, couch, lounger (per seat).png", "PackagePath": "staticwebassets\\Images\\Sofa, couch, lounger (per seat).png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Sonnenschirm.png", "PackagePath": "staticwebassets\\Images\\Sonnenschirm.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Stereo system.png", "PackagePath": "staticwebassets\\Images\\Stereo system.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Stuhl.png", "PackagePath": "staticwebassets\\Images\\Stuhl.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\TV cabinet.png", "PackagePath": "staticwebassets\\Images\\TV cabinet.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\TV.png", "PackagePath": "staticwebassets\\Images\\TV.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Table over 1 m.png", "PackagePath": "staticwebassets\\Images\\Table over 1 m.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Table over 1-2m.png", "PackagePath": "staticwebassets\\Images\\Table over 1-2m.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Table over 1_2 m.png", "PackagePath": "staticwebassets\\Images\\Table over 1_2 m.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Table over 1_6 m.png", "PackagePath": "staticwebassets\\Images\\Table over 1_6 m.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Table up to 0-6 m.png", "PackagePath": "staticwebassets\\Images\\Table up to 0-6 m.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Table up to 0_6 m.png", "PackagePath": "staticwebassets\\Images\\Table up to 0_6 m.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Table up to 1 m.png", "PackagePath": "staticwebassets\\Images\\Table up to 1 m.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Table up to 1-2 m.png", "PackagePath": "staticwebassets\\Images\\Table up to 1-2 m.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Table up to 1_2 m.png", "PackagePath": "staticwebassets\\Images\\Table up to 1_2 m.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Table up to 1_6 m.png", "PackagePath": "staticwebassets\\Images\\Table up to 1_6 m.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Tischtennisplatte.png", "PackagePath": "staticwebassets\\Images\\Tischtennisplatte.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Toilettenschrank.png", "PackagePath": "staticwebassets\\Images\\Toilettenschrank.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Toy box.png", "PackagePath": "staticwebassets\\Images\\Toy box.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Trampolin.png", "PackagePath": "staticwebassets\\Images\\Trampolin.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Vacuum cleaner.png", "PackagePath": "staticwebassets\\Images\\Vacuum cleaner.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Vogelnest.png", "PackagePath": "staticwebassets\\Images\\Vogelnest.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Wardrobe sliding doors.png", "PackagePath": "staticwebassets\\Images\\Wardrobe sliding doors.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Wardrobe, dismountable.png", "PackagePath": "staticwebassets\\Images\\Wardrobe, dismountable.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Washer.png", "PackagePath": "staticwebassets\\Images\\Washer.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Werkbank, zerlegbar.png", "PackagePath": "staticwebassets\\Images\\Werkbank, zerlegbar.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Werkzeugkoffer.png", "PackagePath": "staticwebassets\\Images\\Werkzeugkoffer.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Werkzeugschrank.png", "PackagePath": "staticwebassets\\Images\\Werkzeugschrank.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Wing.png", "PackagePath": "staticwebassets\\Images\\Wing.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\Wäscheschrank.png", "PackagePath": "staticwebassets\\Images\\Wäscheschrank.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\ceiling lamp6.png", "PackagePath": "staticwebassets\\Images\\ceiling lamp6.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\chair1.png", "PackagePath": "staticwebassets\\Images\\chair1.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\desk chair.png", "PackagePath": "staticwebassets\\Images\\desk chair.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\double bed.png", "PackagePath": "staticwebassets\\Images\\double bed.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\floor lamp2.png", "PackagePath": "staticwebassets\\Images\\floor lamp2.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\laundry chest.png", "PackagePath": "staticwebassets\\Images\\laundry chest.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\null.png", "PackagePath": "staticwebassets\\Images\\null.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\zerlegbarer Regal.png", "PackagePath": "staticwebassets\\Images\\zerlegbarer Regal.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\Images\\zerlegbarer Schrank.png", "PackagePath": "staticwebassets\\Images\\zerlegbarer Schrank.png"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\css\\site.css", "PackagePath": "staticwebassets\\css\\site.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\favicon.ico", "PackagePath": "staticwebassets\\favicon.ico"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\js\\site.js", "PackagePath": "staticwebassets\\js\\site.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\LICENSE", "PackagePath": "staticwebassets\\lib\\bootstrap"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.css.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.js.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "PackagePath": "staticwebassets\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\ckeditor5\\ckeditor.js", "PackagePath": "staticwebassets\\lib\\ckeditor5\\ckeditor.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\ckeditor5\\lang\\de.js", "PackagePath": "staticwebassets\\lib\\ckeditor5\\lang\\de.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\ckeditor5\\lang\\en.js", "PackagePath": "staticwebassets\\lib\\ckeditor5\\lang\\en.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\ckeditor5\\lang\\fr.js", "PackagePath": "staticwebassets\\lib\\ckeditor5\\lang\\fr.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\ckeditor5\\lang\\it.js", "PackagePath": "staticwebassets\\lib\\ckeditor5\\lang\\it.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\datatables-bs5\\datatables-bootstrap5.js", "PackagePath": "staticwebassets\\lib\\datatables-bs5\\datatables-bootstrap5.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\datatables-bs5\\datatables.bootstrap5.css", "PackagePath": "staticwebassets\\lib\\datatables-bs5\\datatables.bootstrap5.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\flatpickr\\flatpickr.min.css", "PackagePath": "staticwebassets\\lib\\flatpickr\\flatpickr.min.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\flatpickr\\flatpickr.min.js", "PackagePath": "staticwebassets\\lib\\flatpickr\\flatpickr.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\jquery-ui\\jquery-ui.css", "PackagePath": "staticwebassets\\lib\\jquery-ui\\jquery-ui.css"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\jquery-ui\\jquery-ui.js", "PackagePath": "staticwebassets\\lib\\jquery-ui\\jquery-ui.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "PackagePath": "staticwebassets\\lib\\jquery-validation-unobtrusive\\LICENSE.txt"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "PackagePath": "staticwebassets\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "PackagePath": "staticwebassets\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "PackagePath": "staticwebassets\\lib\\jquery-validation\\LICENSE.md"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\additional-methods.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\additional-methods.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\jquery.validate.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "PackagePath": "staticwebassets\\lib\\jquery-validation\\dist\\jquery.validate.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\jquery\\LICENSE.txt", "PackagePath": "staticwebassets\\lib\\jquery\\LICENSE.txt"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\jquery\\dist\\jquery.js", "PackagePath": "staticwebassets\\lib\\jquery\\dist\\jquery.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "PackagePath": "staticwebassets\\lib\\jquery\\dist\\jquery.min.js"}, {"Id": "C:\\Users\\<USER>\\Desktop\\Aktuelle Projekte\\TaskNet\\Projekt\\TaskDotNet_07_08_2025\\Admin.TaskDotNet\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "PackagePath": "staticwebassets\\lib\\jquery\\dist\\jquery.min.map"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.Admin.TaskDotNet.Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssetEndpoints.props"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.Admin.TaskDotNet.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.build.Admin.TaskDotNet.props", "PackagePath": "build\\Admin.TaskDotNet.props"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.buildMultiTargeting.Admin.TaskDotNet.props", "PackagePath": "buildMultiTargeting\\Admin.TaskDotNet.props"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.buildTransitive.Admin.TaskDotNet.props", "PackagePath": "buildTransitive\\Admin.TaskDotNet.props"}], "ElementsToRemove": []}