﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <Content Remove="C:\Users\<USER>\.nuget\packages\select.htmltopdf.netcore\23.2.0\contentFiles\any\any\Select.Html.dep" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="ClosedXML" Version="0.104.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="6.0.33">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.33">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="6.0.17" />
    <PackageReference Include="PDFsharp-MigraDoc" Version="6.1.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Comman\Comman.csproj" />
    <ProjectReference Include="..\TaskDotNet.Localization\TaskDotNet.Localization.csproj" />
  </ItemGroup>

</Project>
