
using Microsoft.AspNetCore.Localization;
using Microsoft.EntityFrameworkCore;
using TaskDotNet.Comman.DataAccess;
using TaskDotNet.Helper;
using TaskDotNet.Helper.Extensions;
using TaskDotNet.Models;


var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddServices(builder.Configuration);

var app = builder.Build();

// Configure the HTTP request pipeline.

app.UseRequestLocalization(new RequestLocalizationOptions
{
    DefaultRequestCulture = new RequestCulture("en-US"),
    SupportedCultures = Constants.supportedCultures,
    SupportedUICultures = Constants.supportedCultures,
    RequestCultureProviders = new List<IRequestCultureProvider>
                {
                new QueryStringRequestCultureProvider(),
                new CookieRequestCultureProvider()
                }
});

if (app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error");
    app.UseStatusCodePagesWithReExecute("/Error/{0}");

    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();

app.UseAuthentication();
app.UseAuthorization();
app.UseSession(); // Enable session

SeedAdminUser(app);

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

app.MapRazorPages();

app.Run();


void SeedAdminUser(WebApplication app)
{
    using var scope = app.Services.CreateScope();
    var services = scope.ServiceProvider;
    var context = services.GetRequiredService<ApplicationDbContext>();

    //migrations if they are not applied
    if (context.Database.GetPendingMigrations().Count() > 0)
    {
        context.Database.Migrate();
    }

    // Check if there are any users in the database
    if (!context.Users.Any())
    {
        // Create an admin user
        var adminUser = new AdminUser
        {
            UserName = "shennawi2024",
            Name = "shennawi2024",
            Email = "<EMAIL>",
            Password = "@shennawi"
        };


        context.AdminUsers.Add(adminUser);
        context.SaveChanges();
    }
}
