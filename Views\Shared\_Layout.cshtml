@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.Localization
@using System.Security.Claims

@{

    string companyName = User.FindFirst(ClaimTypes.GivenName)?.Value;

    string currentLanguage = ViewContext.HttpContext.Features.Get<IRequestCultureFeature>().RequestCulture.Culture.Name;

    var confirmDeleteTitle = @SharedLocalizer["Delete record"];

    var confirmDeleteText = @SharedLocalizer["Are you sure you want to delete the selected entry?"];

    var yesDeleteText = @SharedLocalizer["Yes"];

    var noDeleteText = @SharedLocalizer["No"];
}
<!DOCTYPE html>

<!-- =========================================================
* Sneat - Bootstrap 5 HTML Admin Template - Pro | v1.0.0
==============================================================

* Product Page: https://themeselection.com/products/sneat-bootstrap-html-admin-template/
* Created by: ThemeSelection
* License: You must have a valid license purchased in order to legally use the theme for your project.
* Copyright ThemeSelection (https://themeselection.com)

=========================================================
 -->
<!-- beautify ignore:start -->
<html lang="en"
      class="light-style layout-menu-fixed"
      dir="ltr"
      data-theme="theme-default"
      data-assets-path="~/Dashboard/assets/"
      data-template="vertical-menu-template-free">
<head>
    <meta charset="utf-8" />
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />

    <title>Dashboard - TaskDotNet </title>

    <meta name="description" content="" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="~/Dashboard/assets/img/favicon/favicon.ico" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Public+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap"
          rel="stylesheet" />

    <!-- Icons. Uncomment required icon fonts -->
    <link rel="stylesheet" href="~/Dashboard/assets/vendor/fonts/boxicons.css" />

    <!-- Core CSS -->
    <link rel="stylesheet" href="~/Dashboard/assets/vendor/css/core.css" class="template-customizer-core-css" asp-append-version="true" />
    <link rel="stylesheet" href="~/Dashboard/assets/vendor/css/theme-default.css" class="template-customizer-theme-css" />
    <link rel="stylesheet" href="~/Dashboard/assets/css/demo.css" />
    <link href="~/lib/flatpickr/flatpickr.min.css" rel="stylesheet" />

    <!-- Vendors CSS -->
    <link rel="stylesheet" href="~/Dashboard/assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.css" />

    <!-- Page CSS -->
    <!-- Helpers -->
    <script src="~/Dashboard/assets/vendor/js/helpers.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.css" rel="stylesheet">

    <!--! Template customizer & Theme config files MUST be included after core stylesheets and helpers.js in the <head> section -->
    <!--? Config:  Mandatory theme config file contain global vars & default theme options, Set your preferred theme option in this file.  -->
    <script src="~/Dashboard/assets/js/config.js"></script>
    @await RenderSectionAsync("Links", required: false)

    <style>
        @@media (max-width: 767.98px) {
            .ClockDate {
                display: none;
            }

            .lang {
                display: none;
            }
        }

        .modal-content {
            border-radius: 1rem;
        }
    </style>


</head>

<body>
    <!-- Layout wrapper -->
    <div class="layout-wrapper layout-content-navbar">
        <div class="layout-container">
            <!-- Menu -->
            <!-- Loader -->
            <div id="loader" class="d-none">
                <div class="spinner"></div>
                <p>Loading...</p>
            </div>

            <aside id="layout-menu" class="layout-menu menu-vertical menu bg-menu-theme">
                <div class="app-brand demo">
                    <a asp-controller="Home" asp-action="Index" class="app-brand-link">
                        <span class="app-brand-logo demo">
                            <img class="logoImg" style="width:140px" src="~/Dashboard/assets/img/logo.png">

                        </span>
                    </a>

                    <a href="javascript:void(0);" class="layout-menu-toggle menu-link text-large ms-auto d-block d-xl-none">
                        <i class="bx bx-chevron-left bx-sm align-middle"></i>
                    </a>
                </div>

                <div class="menu-inner-shadow"></div>

                <ul class="menu-inner py-1">
                    <!-- Dashboard -->
                    <li class="menu-item">
                        <a asp-controller="Home" asp-action="Index" class="menu-link">
                            <i class="menu-icon tf-icons bx bx-home-circle"></i>
                            <div data-i18n="Analytics">@SharedLocalizer["Information Sheet"]</div>
                        </a>
                    </li>

                    <!-- Layouts -->

                    <li class="menu-header small text-uppercase">
                        <span class="menu-header-text">@SharedLocalizer["Pages"]</span>
                    </li>

                    <li class="menu-item">
                        <a asp-controller="Partners" asp-action="AllPartners" class="menu-link">
                            <i class="menu-icon tf-icons bx bx-user"></i>
                            <div data-i18n="Basic">@SharedLocalizer["All Paertners"]</div>
                        </a>
                    </li>

                    <li class="menu-item">
                        <a asp-controller="Activities" asp-action="GetAllActivities" class="menu-link">
                            <i class="menu-icon tf-icons bx bx-collection"></i>
                            <div data-i18n="Basic">@SharedLocalizer["Activities List"]</div>
                        </a>
                    </li>

                    <li class="menu-item">
                        <a asp-controller="NewActivities" asp-action="Index" class="menu-link">
                            <i class="menu-icon tf-icons bx bx-list-check"></i>
                            <div data-i18n="Basic">@SharedLocalizer["New Activities"]</div>
                        </a>
                    </li>

                    <li class="menu-item">
                        <a asp-controller="Archive" asp-action="Index" class="menu-link ">
                            <i class="menu-icon tf-icons bx bx-dock-top"></i>
                            <div>@SharedLocalizer["Archive"]</div>
                        </a>

                    </li>
                    
                    <li class="menu-item">
                        <a asp-controller="ContactUs" asp-action="Index" class="menu-link ">
                            <i class="menu-icon tf-icons bx bx-envelope"></i>
                            <div>@SharedLocalizer["ContactUs"]</div>
                        </a>

                    </li>

                    <li class="menu-item">
                        <a asp-controller="OrdersCount" asp-action="Index" class="menu-link ">
                            <i class="menu-icon tf-icons bx bx-collection"></i>
                            <div>@SharedLocalizer["RequestControl"]</div>
                        </a>

                    </li>
                    @if (User.IsInRole("Admin"))
                    {
                        <li class="menu-item">
                            <a asp-controller="Statistics" asp-action="AdminStatistics" class="menu-link">
                                <i class="menu-icon tf-icons bx bx-collection"></i>
                                <div data-i18n="Basic">@SharedLocalizer["Statistics"]</div>
                            </a>
                        </li>
                    }
                    @if (User.IsInRole("Admin"))
                    {
                        <li class="menu-item" style="">
                            <a href="javascript:void(0);" class="menu-link menu-toggle">
                                <i class="menu-icon tf-icons bx bx-cog"></i>
                                <div class="text-truncate" data-i18n="Roles &amp; Permissions"> @SharedLocalizer["Settings"]</div>
                            </a>
                            <ul class="menu-sub">

                                <li class="menu-item">
                                    <a asp-controller="Company" asp-action="Index" class="menu-link ">
                                        <i class="menu-icon tf-icons bx bx-dock-top"></i>
                                        <div>@SharedLocalizer["Company Data"]</div>
                                    </a>

                                </li>


                                <li class="menu-item">
                                    <a asp-controller="ResetData" asp-action="Reset" class="menu-link ">
                                        <i class="menu-icon tf-icons bx bx-collection"></i>
                                        <div>@SharedLocalizer["Daten reinigen"]</div>
                                    </a>

                                </li>

                                <li class="menu-item">
                                    <a asp-controller="Users" asp-action="Index" class="menu-link ">
                                        <i class='menu-icon tf-icons bx bxs-user-circle'></i>
                                        <div>@SharedLocalizer["Manage Users"]</div>
                                    </a>

                                </li>

                                <li class="menu-item">
                                    <a asp-controller="Inventory" asp-action="Index" class="menu-link ">
                                        <i class="menu-icon tf-icons bx bx-dock-top"></i>
                                        <div>@SharedLocalizer["Inventory"]</div>
                                    </a>

                                </li>
                                <li class="menu-item">
                                    <a asp-controller="InventoryItems" asp-action="index" class="menu-link ">
                                        <i class="menu-icon tf-icons bx bx-dock-top"></i>
                                        <div>@SharedLocalizer["Inventory Items"]</div>
                                    </a>

                                </li>
                                <li class="menu-item">
                                    <a asp-controller="EmailText" asp-action="Index" class="menu-link ">
                                        <i class="menu-icon tf-icons bx bx-dock-top"></i>
                                        <div>@SharedLocalizer["Email texts"]</div>
                                    </a>

                                </li>
                                <li class="menu-item">
                                    <a asp-controller="Advertising" asp-action="Index" class="menu-link ">
                                        <i class="menu-icon tf-icons bx bx-envelope"></i>
                                        <div>@SharedLocalizer["Email senden"]</div>
                                    </a>

                                </li>

                                <li class="menu-item">
                                    <a asp-controller="Credits" asp-action="Index" class="menu-link ">
                                        <i class="menu-icon tf-icons bx bx-credit-card"></i>
                                        <div>@SharedLocalizer["Credits"]</div>
                                    </a>

                                </li>

                            </ul>
                        </li>
                    }
                    <li class="menu-item text-center mt-3">
                        <img src="~/dashboard/assets/img/admin.png" alt="side view" />
                    </li>
                </ul>
            </aside>
            <!-- / Menu -->
            <!-- Layout container -->
            <div class="layout-page">
                <!-- Navbar -->

                <nav class="layout-navbar mb-3 navbar navbar-expand-xl navbar-detached align-items-center bg-navbar-theme"
                     id="layout-navbar">
                    <div class="layout-menu-toggle navbar-nav align-items-xl-center me-3 me-xl-0 d-xl-none">
                        <a class="nav-item nav-link px-0 me-xl-4" href="javascript:void(0)">
                            <i class="bx bx-menu bx-sm"></i>
                        </a>
                    </div>

                    <div class="navbar-nav-right d-flex align-items-center" id="navbar-collapse">
                        <div class="nav-item d-flex align-items-center mx-2">
                            <span class="logo-container align-content-center d-flex justify-content-cente fs-4">
                                @companyName
                            </span>
                        </div>
                        <!-- Search -->
                        <div class="ClockDate navbar-nav align-items-center mx-auto">

                            <div class="nav-item d-flex align-items-center mx-2">
                                <i class='bx bxs-calendar mx-2' style="font-size:25px"></i>
                                <span id="date" style="font-size:20px"></span>

                                <i class='bx bxs-time mx-2' style="font-size:25px"></i>
                                <span id="clock" style="font-size:20px"></span>


                            </div>

                        </div>

                        <ul class="navbar-nav flex-row align-items-center ms-auto">


                            <li class="nav-item d-flex navbar-dropdown dropdown-user dropdown mx-4">
                                <a class="nav-link dropdown-toggle hide-arrow  d-flex justify-content-between" data-bs-toggle="dropdown"
                                   href="@Url.Action("SetLanguage", "Language", new {
                                                  culture = "de-DE",
                                                  returnUrl = Context.Request.Path + Context.Request.QueryString
                                              })">

                                    @if (currentLanguage == "de-DE")
                                    {
                                        <img src="~/Dashboard/assets/img/de-flag.png" width="35" height="35" class="mt-1 mx-2" />
                                    }
                                    @if (currentLanguage == "en-US")
                                    {
                                        <img src="~/Dashboard/assets/img/us-flag.png" width="35" height="35" class="mt-1 mx-2" />
                                    }
                                    @if (currentLanguage == "it-IT")
                                    {
                                        <img src="~/Dashboard/assets/img/it-flag.png" width="35" height="35" class="mt-1 mx-2" />
                                    }
                                    @if (currentLanguage == "fr-FR")
                                    {
                                        <img src="~/Dashboard/assets/img/fr-flag.png" width="35" height="35" class="mt-1 mx-2" />

                                    }
                                    <span class="lang mt-2">@SharedLocalizer["Language"] </span>
                                </a>

                                <ul class="dropdown-menu dropdown-menu-end">



                                    <li>
                                        <a class="dropdown-item d-flex justify-content-between"
                                           href="@Url.Action("SetLanguage", "Language", new {
                                                  culture = "de-DE",
                                                  returnUrl = Context.Request.Path + Context.Request.QueryString
                                              })">
                                            <img src="~/Dashboard/assets/img/de-flag.png" width="25" height="25" class="mt-1" />
                                            <span>German</span>
                                        </a>

                                    </li>

                                    <li>
                                        <div class="dropdown-divider"></div>
                                    </li>



                                    <li>
                                        <a class="dropdown-item d-flex justify-content-between"
                                           href="@Url.Action("SetLanguage", "Language", new {
                                                  culture = "en-US",
                                                  returnUrl = Context.Request.Path + Context.Request.QueryString
                                              })">
                                            <img src="~/Dashboard/assets/img/us-flag.png" width="25" height="25" class="mt-1" />
                                            <span> English</span>
                                        </a>
                                    </li>
                                    <li>
                                        <div class="dropdown-divider"></div>
                                    </li>
                                    <li>
                                        <a class="dropdown-item d-flex justify-content-between"
                                           href="@Url.Action("SetLanguage", "Language", new {
                                                  culture = "it-IT",
                                                  returnUrl = Context.Request.Path + Context.Request.QueryString
                                              })">
                                            <img src="~/Dashboard/assets/img/it-flag.png" width="25" height="25" class="mt-1" />
                                            <span>Italy</span>
                                        </a>

                                    </li>

                                    <li>
                                        <div class="dropdown-divider"></div>
                                    </li>

                                    <li>
                                        <a class="dropdown-item d-flex justify-content-between"
                                           href="@Url.Action("SetLanguage", "Language", new {
                                                  culture = "fr-FR",
                                                  returnUrl = Context.Request.Path + Context.Request.QueryString
                                              })">
                                            <img src="~/Dashboard/assets/img/fr-flag.png" width="25" height="25" class="mt-1" />
                                            <span>France</span>
                                        </a>

                                    </li>
                                </ul>
                            </li>

                            <li class="nav-item">
                                <a class=nav-link id="showNotifications" href="#">
                                    @await Component.InvokeAsync("Notifications")
                                </a>
                            </li>
                            <!-- User -->
                            <li class="nav-item navbar-dropdown dropdown-user dropdown">
                                <a class="nav-link dropdown-toggle hide-arrow" href="javascript:void(0);" data-bs-toggle="dropdown">
                                    <div class="avatar avatar-online">
                                        <img src="~/Dashboard/assets/img/avatars/1.png" alt class="w-px-40 h-auto rounded-circle" />
                                    </div>

                                </a>
                                <ul class="dropdown-menu dropdown-menu-end">


                                    <li>

                                        <a class="dropdown-item" href="#">
                                            <div class="d-flex">
                                                <div class="flex-shrink-0 me-3">
                                                    <div class="avatar avatar-online">
                                                        <img src="~/Dashboard/assets/img/avatars/1.png" alt="" class="w-px-40 h-auto rounded-circle" />
                                                    </div>
                                                </div>
                                                <div class=" flex-grow-1">
                                                    <span class="fw-semibold d-block">

                                                        <span class="user-name">@User.Identity.Name</span>

                                                    </span>
                                                    <small class="text-muted">Admin</small>

                                                </div>
                                            </div>
                                        </a>
                                    </li>

                                    <li>
                                        <div class="dropdown-divider"></div>
                                    </li>

                                    @if (User.IsInRole("Admin"))
                                    {
                                        <li>
                                            <a class="dropdown-item" asp-page="/Account/Manage/Settings">
                                                <i class="bx bx-cog me-2"></i>
                                                <span class="align-middle"> @SharedLocalizer["Account Settings"]</span>
                                            </a>
                                        </li>
                                    }
                                    <li>
                                        <div class="dropdown-divider"></div>
                                    </li>

                                    <li>
                                        <a class="dropdown-item">
                                            <form asp-controller="Account" asp-action="LogOut">
                                                <i class="bx bx-power-off me-2"></i>
                                                <input type="submit" class="align-middle" style="border:none; background:none" value="@SharedLocalizer["Log Out"] " />
                                            </form>

                                        </a>
                                    </li>
                                </ul>
                            </li>

                            <!--/ User -->
                        </ul>
                    </div>
                </nav>

                <!-- Notification Sound -->
                <audio id="notificationSound" src="~/dashboard/assets/notification.wav" preload="auto" hidden></audio>
                <!-- / Navbar -->
                <!-- Overlay -->
                @RenderBody()
                <div class="layout-overlay layout-menu-toggle"></div>

                <!-- Notifications Container -->
                <div id="notificationsContainer"></div>

            </div>
            <!-- / Layout wrapper -->



        </div>
    </div>

    <!-- Core JS -->
    <!-- build:js assets/vendor/js/core.js -->

    <script src="~/Dashboard/assets/vendor/libs/jquery/jquery.js"></script>
    <script src="~/Dashboard/assets/vendor/libs/popper/popper.js"></script>
    <script src="~/Dashboard/assets/vendor/js/bootstrap.js"></script>
    <script src="~/Dashboard/assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.js"></script>

    <script src="~/Dashboard/assets/vendor/js/menu.js"></script>
    <script src="~/lib/flatpickr/flatpickr.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- endbuild -->
    <script src="//cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/js/toastr.min.js"></script>
    <partial name="~/Views/Shared/_Notification.cshtml" />

    <!-- Main JS -->
    <script src="~/js/site.js" asp-append-version="true"></script>
    <script src="~/Dashboard/assets/js/main.js"></script>
    <script>

        // SweetAlert2 confirmation for archive buttons
        document.querySelectorAll('.delete-btn').forEach(button => {
            button.addEventListener('click', function () {
                var url = this.getAttribute('data-url');
                Swal.fire({
                    title: "@confirmDeleteTitle",
                    text: "@Html.Raw(confirmDeleteText)",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: '@yesDeleteText',
                    cancelButtonText: '@noDeleteText'
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: url,
                            type: 'GET',
                            success: function (result) {
                                location.reload(true);
                            },
                            error: function (xhr) {
                                alert("@SharedLocalizer["SomeThingWentWrong"]");
                                location.reload(true);
                            }
                        });
                    }
                });
            });
        });



    </script>
    <script>
        // Button click event
        document.getElementById("showNotifications").addEventListener("click", async function () {
            // Fetch partial view content with notifications
            const response = await fetch('/Notifications/LoadNotifications'); // Replace with your controller action
            if (response.ok) {
                const notifications = await response.json(); // Assuming the partial view returns JSON data
                displayNotifications(notifications);
            } else {
                console.error("Failed to load notifications.");
            }
        });

        function displayNotifications(notifications) {
            const container = document.getElementById("notificationsContainer");
            const notificationSound = document.getElementById("notificationSound");

            container.innerHTML = ""; // Clear any previous notifications
            let currentNotification = 0;

            // Create modals dynamically
            notifications.forEach((notification, index) => {
                const modalId = `notificationModal${index}`;
                const modalHTML = `
                                    <div class="modal fade" id="${modalId}" tabindex="-1" role="dialog" aria-labelledby="${modalId}Label" aria-hidden="true">
                                      <div class="modal-dialog" role="document">
                                        <div class="modal-content py-md-5 px-md-4 p-sm-3 p-4 text-center">
                                          <h3>Neue Bestellung</h3>
                                          <i class='bx bxs-bell text-primary'style="font-size:90px; padding: 30px 0px;" ></i>
                                          <i class="fa fa-bell text-danger"></i>
                                          <p class="px-md-5 px-sm-1" style="color: #c1c1c1;font-weight: 500;">
                                            Eine neue Bestellung ist eingetroffen.
                                          </p>
                                          <div class="mx-auto text-start">
                                               <p><strong>Kunde:</strong> ${notification.salute} ${notification.name}</p>
                                              <p><strong>Adresse:</strong> ${notification.postBox}, ${notification.city}</p>
                                              <p><strong>Aktivität:</strong> ${notification.activityType}, am: ${notification.excuteDate}</p>
                                          </div>
                                          <div class="text-center mb-3">
                                            <button class="btn btn-primary w-50 rounded-pill" style="font-size: 17px;" data-bs-dismiss="modal">OK</button>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  `;
                container.innerHTML += modalHTML;
            });

            function showNextNotification() {
                if (currentNotification >= notifications.length) {
                    // Reload the page after all notifications are shown
                    window.location.reload();
                    return;
                }


                const modalId = `notificationModal${currentNotification}`;
                const modal = new bootstrap.Modal(document.getElementById(modalId));

                // Play sound and show modal
                notificationSound.play().catch((error) => console.warn("Audio play failed:", error));
                modal.show();

                // Move to the next notification after modal is closed
                document.getElementById(modalId).addEventListener("hidden.bs.modal", () => {
                    currentNotification++;
                    showNextNotification();
                });
            }

            // Start showing notifications
            showNextNotification();
        }
    </script>
    <!-- Place this tag in your head or just before your close body tag. -->
    <script async defer src="https://buttons.github.io/buttons.js"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <!-- Another JS -->
    @await RenderSectionAsync("Scripts", required: false)


</body>
</html>