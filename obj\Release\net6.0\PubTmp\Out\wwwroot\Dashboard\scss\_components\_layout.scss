// Layouts
// *******************************************************************************

.layout-container {
  min-height: 100vh;
}

.layout-wrapper,
.layout-container {
  width: 100%;
  display: flex;
  flex: 1 1 auto;
  align-items: stretch;
}

.layout-page,
.content-wrapper,
.content-wrapper > *,
.layout-menu {
  min-height: 1px;
}

.layout-navbar,
.content-footer {
  flex: 0 0 auto;
}

.layout-page {
  display: flex;
  flex: 1 1 auto;
  align-items: stretch;
  padding: 0;

  // Without menu
  .layout-without-menu & {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
}

.content-wrapper {
  display: flex;
  align-items: stretch;
  flex: 1 1 auto;
  flex-direction: column;
  justify-content: space-between;
}
// Content backdrop
.content-backdrop {
  // z-index: 1 (layout static)
  @include overlay-backdrop(1, $modal-backdrop-bg, $modal-backdrop-opacity);
  // z-index: 10 (layout fixed)
  .layout-menu-fixed & {
    z-index: 10;
  }
  &.fade {
    z-index: -1;
  }
}

// * Layout Navbar
// *******************************************************************************
.layout-navbar {
  position: relative;
  padding-top: 0.25rem;
  padding-bottom: 0.2rem;
  height: $navbar-height;
  flex-wrap: nowrap;
  color: $body-color;
  z-index: 2;

  .navbar {
    transform: translate3d(0, 0, 0);
  }
  .navbar-nav-right {
    flex-basis: 100%;
  }

  .navbar-search-wrapper {
    .search-input,
    .input-group-text {
      background-color: transparent;
    }
  }
  // Style for detached navbar
  &.navbar-detached {
    // Container layout max-width
    $container-xxl: map-get($container-max-widths, xxl);
    &.container-xxl {
      max-width: calc(#{$container-xxl} - calc(#{$container-padding-x} * 2));
    }
    // Navbar fixed
    .layout-navbar-fixed & {
      width: calc(100% - calc(#{$container-padding-x} * 2) - #{$menu-width});
      @include media-breakpoint-down(xl) {
        width: calc(100% - calc(#{$container-padding-x} * 2)) !important;
      }
      @include media-breakpoint-down(lg) {
        width: calc(100% - calc(#{$container-padding-x-sm} * 2)) !important;
      }
    }
    .layout-navbar-fixed.layout-menu-collapsed & {
      width: calc(100% - calc(#{$container-padding-x} * 2) - #{$menu-collapsed-width});
    }

    // Navbar static
    width: calc(100% - calc(#{$container-padding-x} * 2));
    @include media-breakpoint-down(xl) {
      width: calc(100vw - (100vw - 100%) - calc(#{$container-padding-x} * 2)) !important;
    }
    @include media-breakpoint-down(lg) {
      width: calc(100vw - (100vw - 100%) - calc(#{$container-padding-x-sm} * 2)) !important;
    }
    .layout-menu-collapsed &,
    .layout-without-menu & {
      width: calc(100% - calc(#{$container-padding-x} * 2));
    }

    margin: 0.75rem auto 0;
    border-radius: $border-radius;
    padding: 0 $card-spacer-x;
  }

  .search-input-wrapper {
    .search-toggler {
      position: absolute;
      top: 1.25rem;
      right: 1rem;
      z-index: 1;
    }

    .search-input {
      height: 100%;
      box-shadow: none;
    }
  }
  &[class*='bg-']:not(.bg-navbar-theme) {
    .nav-item {
      .input-group-text,
      .dropdown-toggle {
        color: $white;
      }
    }
  }

  @include media-breakpoint-down(xl) {
    .navbar-nav {
      .nav-item.dropdown {
        .dropdown-menu {
          position: absolute;
          .last-login {
            white-space: nowrap;
          }
        }
      }
    }
  }
  @include media-breakpoint-down(md) {
    .navbar-nav {
      .nav-item.dropdown {
        position: static;
        float: left;
        .badge-notifications {
          top: auto;
        }
        .dropdown-menu {
          position: absolute;
          left: 0.9rem;
          min-width: auto;
          width: 92%;
        }
      }
    }
  }
}

// * Navbar require high z-index as we use z-index for menu slide-out for below large screen
@include media-breakpoint-down(xl) {
  .layout-navbar {
    z-index: $zindex-menu-fixed;
  }
}

// * Layout Menu
// *******************************************************************************
.layout-menu {
  position: relative;
  flex: 1 0 auto;

  .menu {
    transform: translate3d(0, 0, 0);
  }

  .menu-vertical {
    height: 100%;
  }
}

// * Layout Content navbar
// *******************************************************************************

.layout-content-navbar {
  .layout-page {
    flex-basis: 100%;
    flex-direction: column;
    width: 0;
    min-width: 0;
    max-width: 100%;
  }

  .content-wrapper {
    width: 100%;
  }
}

// * Fixed and fixed off-canvas layout (Layout Fixed)
// *******************************************************************************

@include media-breakpoint-up(xl) {
  // Menu

  .layout-menu-fixed,
  .layout-menu-fixed-offcanvas {
    .layout-menu {
      position: fixed;
      top: 0;
      bottom: 0;
      left: 0;
      margin-right: 0 !important;
      margin-left: 0 !important;
    }
  }

  // Container

  // Menu expanded
  .layout-menu-fixed:not(.layout-menu-collapsed),
  .layout-menu-fixed-offcanvas:not(.layout-menu-collapsed) {
    .layout-page {
      padding-left: $menu-width;
    }
  }
}

// Reset paddings (for non fixed entities)
html:not(.layout-navbar-fixed):not(.layout-menu-fixed):not(.layout-menu-fixed-offcanvas) .layout-page,
html:not(.layout-navbar-fixed) .layout-content-navbar .layout-page {
  padding-top: 0 !important;
}
html:not(.layout-footer-fixed) .content-wrapper {
  padding-bottom: 0 !important;
}

@include media-breakpoint-down(xl) {
  .layout-menu-fixed .layout-wrapper.layout-navbar-full .layout-menu,
  .layout-menu-fixed-offcanvas .layout-wrapper.layout-navbar-full .layout-menu {
    top: 0 !important;
  }

  html:not(.layout-navbar-fixed) .layout-navbar-full .layout-page {
    padding-top: 0 !important;
  }
}

// * Fixed navbar layout
// *******************************************************************************

.layout-navbar-fixed {
  .layout-navbar {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
  }
}

@include media-breakpoint-up(xl) {
  // Fix navbar within Navbar Full layout in fixed mode
  .layout-menu-fixed .layout-navbar-full .layout-navbar,
  .layout-menu-fixed-offcanvas .layout-navbar-full .layout-navbar {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
  }

  // Fix navbar within Content Navbar layout in fixed mode - Menu expanded
  .layout-navbar-fixed:not(.layout-menu-collapsed),
  .layout-menu-fixed.layout-navbar-fixed:not(.layout-menu-collapsed),
  .layout-menu-fixed-offcanvas.layout-navbar-fixed:not(.layout-menu-collapsed) {
    .layout-content-navbar:not(.layout-without-menu) .layout-navbar {
      left: $menu-width;
    }
  }
}

// * Fixed footer
// *******************************************************************************

.layout-footer-fixed .content-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
}

@include media-breakpoint-up(xl) {
  // Fixed footer - Menu expanded
  .layout-footer-fixed:not(.layout-menu-collapsed) {
    .layout-wrapper:not(.layout-without-menu) .content-footer {
      left: $menu-width;
    }
  }
}

// * Small screens layout
// *******************************************************************************

@include media-breakpoint-down(xl) {
  .layout-menu {
    position: fixed !important;
    top: 0 !important;
    height: 100% !important;
    left: 0 !important;
    margin-right: 0 !important;
    margin-left: 0 !important;
    transform: translate3d(-100%, 0, 0);
    will-change: transform, -webkit-transform;

    .layout-menu-expanded & {
      transform: translate3d(0, 0, 0) !important;
    }
  }

  .layout-menu-expanded body {
    overflow: hidden;
  }

  .layout-overlay {
    position: fixed;
    top: 0;
    right: 0;
    height: 100% !important;
    left: 0;
    display: none;
    background: $modal-backdrop-bg;
    opacity: $modal-backdrop-opacity;
    cursor: pointer;

    .layout-menu-expanded & {
      display: block;
    }
  }

  .layout-menu-100vh .layout-menu,
  .layout-menu-100vh .layout-overlay {
    height: 100vh !important;
  }
}

// * Z-Indexes
// *******************************************************************************

// Navbar (fixed)
.layout-navbar-fixed body:not(.modal-open),
.layout-menu-fixed body:not(.modal-open),
.layout-menu-fixed-offcanvas body:not(.modal-open) {
  .layout-navbar-full .layout-navbar {
    z-index: $zindex-menu-fixed;
  }

  .layout-content-navbar .layout-navbar {
    z-index: $zindex-menu-fixed - 5;
  }
}

// Footer (fixed)
.layout-footer-fixed .content-footer {
  z-index: $zindex-fixed;
}

@include media-breakpoint-down(xl) {
  .layout-menu {
    z-index: $zindex-layout-mobile;
  }

  .layout-overlay {
    z-index: $zindex-layout-mobile - 1;
  }
}

@include media-breakpoint-up(xl) {
  // Default expanded

  // Navbar full layout
  .layout-navbar-full {
    .layout-navbar {
      z-index: 10;
    }

    .layout-menu {
      z-index: 9;
    }
  }
  // Content Navbar layout
  .layout-content-navbar {
    .layout-navbar {
      z-index: 9;
    }

    .layout-menu {
      z-index: 10;
    }
  }

  // Fixed
  // Navbar full layout
  .layout-menu-fixed body:not(.modal-open) .layout-navbar-full .layout-menu,
  .layout-menu-fixed-offcanvas body:not(.modal-open) .layout-navbar-full .layout-menu {
    z-index: $zindex-menu-fixed - 5;
  }
  // Content Navbar layout
  .layout-navbar-fixed body:not(.modal-open) .layout-content-navbar .layout-menu,
  .layout-menu-fixed body:not(.modal-open) .layout-content-navbar .layout-menu,
  .layout-menu-fixed-offcanvas body:not(.modal-open) .layout-content-navbar .layout-menu {
    z-index: $zindex-menu-fixed;
  }
}

// * Transitions and animations
// *******************************************************************************

// Disable navbar link hover transition
.layout-menu-link-no-transition {
  .layout-menu .menu-link,
  .layout-menu-horizontal .menu-link {
    transition: none !important;
    animation: none !important;
  }
}

// Disable navbar link hover transition
.layout-no-transition .layout-menu,
.layout-no-transition .layout-menu-horizontal {
  &,
  & .menu,
  & .menu-item {
    transition: none !important;
    animation: none !important;
  }
}

@include media-breakpoint-down(xl) {
  .layout-transitioning {
    .layout-overlay {
      animation: menuAnimation $menu-animation-duration;
    }

    .layout-menu {
      transition-duration: $menu-animation-duration;
      transition-property: transform, -webkit-transform;
    }
  }
}

@include media-breakpoint-up(xl) {
  .layout-menu-collapsed:not(.layout-transitioning):not(.layout-menu-offcanvas):not(.layout-menu-fixed):not(.layout-menu-fixed-offcanvas)
    .layout-menu {
    transition-duration: $menu-animation-duration;
    transition-property: margin-left, margin-right, width;
  }

  .layout-transitioning {
    &.layout-menu-offcanvas .layout-menu {
      transition-duration: $menu-animation-duration;
      transition-property: margin-left, margin-right, transform, -webkit-transform;
    }

    &.layout-menu-fixed,
    &.layout-menu-fixed-offcanvas {
      .layout-page {
        transition-duration: $menu-animation-duration;
        transition-property: padding-left, padding-right;
      }
    }

    &.layout-menu-fixed {
      .layout-menu {
        transition: width $menu-animation-duration;
      }
    }

    &.layout-menu-fixed-offcanvas {
      .layout-menu {
        transition-duration: $menu-animation-duration;
        transition-property: transform, -webkit-transform;
      }
    }

    &.layout-navbar-fixed .layout-content-navbar .layout-navbar,
    &.layout-footer-fixed .content-footer {
      transition-duration: $menu-animation-duration;
      transition-property: left, right;
    }

    &:not(.layout-menu-offcanvas):not(.layout-menu-fixed):not(.layout-menu-fixed-offcanvas) .layout-menu {
      transition-duration: $menu-animation-duration;
      transition-property: margin-left, margin-right, width;
    }
  }
}

// Disable transitions/animations in IE 10-11
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
  .menu,
  .layout-menu,
  .layout-page,
  .layout-navbar,
  .content-footer {
    transition: none !important;
    transition-duration: 0s !important;
  }
  .layout-overlay {
    animation: none !important;
  }
}

@include keyframes(menuAnimation) {
  0% {
    opacity: 0;
  }
  100% {
    opacity: $modal-backdrop-opacity;
  }
}
