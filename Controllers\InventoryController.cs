﻿using Admin.TaskDotNet.Dtos;
using Comman.Services.Interfaces;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TaskDotNet.Models;
using Admin.TaskDotNet.Helper;

namespace Admin.TaskDotNet.Controllers
{
    [Authorize(Roles = "Admin,Moderator")]
    public class InventoryController : Controller
    {
        #region Ctor
        private readonly IMapper mapper;
        private readonly IInventoryService inventoryService;
        public InventoryController(IMapper mapper, IInventoryService inventoryService)
        {
            this.mapper = mapper;
            this.inventoryService = inventoryService;
        }
        #endregion

        #region Index
        public async Task<IActionResult> Index()
        {
            var data = await inventoryService.Get();
            var model = mapper.Map<IEnumerable<InventoryDto>>(data);
            return View(model);
        }
        #endregion

        #region Create
        [HttpGet]
        public IActionResult Create()
        {
            return View();
        }
        [HttpPost]
        public async Task<IActionResult> Create(InventoryDto dto)
        {
            try
            {
                var data = mapper.Map<Inventory>(dto);
                await inventoryService.Create(data);

                if (data != null)
                {
                    //  TempData[key: "InventoryCreated"] = "done";
                    return RedirectToAction("Index");
                }
            }
            catch (Exception)
            {
                TempData[key: "ErrorMessage"] = "error";
                return View();

            }
            TempData[key: "ErrorMessage"] = "error";
            return View();


        }
        #endregion

        #region Edit
        [HttpGet]
        public async Task<IActionResult> Edit(int id)
        {
            var data = await inventoryService.GetById(id);
            var model = mapper.Map<InventoryDto>(data);
            return View(model);
        }
        [HttpPost]
        public IActionResult Edit(InventoryDto dto)
        {
            try
            {
                var data = mapper.Map<Inventory>(dto);
                inventoryService.Update(data);

                if (data != null)
                {
                    // TempData[key: "InventoryUpdated"] = "done";
                    return RedirectToAction("Index");
                }
            }
            catch (Exception)
            {
                TempData[key: "ErrorMessage"] = "error";
                return View();

            }
            TempData[key: "ErrorMessage"] = "error";
            return View();


        }
        #endregion

        #region Delete 
        public async Task<IActionResult> Delete(int id)
        {
            var data = await inventoryService.GetById(id);
            if (data != null)
            {
                inventoryService.Delete(data);
                return Json(true);

            }
            TempData[key: "ErrorMessage"] = "error";
            return Json(false);
        }
        #endregion

    }  
}
