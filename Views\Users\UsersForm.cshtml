﻿@using Admin.TaskDotNet.Dtos
@model UserDto

@{
    ViewBag.Title = string.IsNullOrEmpty(Model.Id) ? @SharedLocalizer["Create"] : @SharedLocalizer["Update"];
    var header = string.IsNullOrEmpty(Model.Id) ? @SharedLocalizer["CreateUser"] : @SharedLocalizer["UpdateUser"];
}

<!-- Content -->

<div class="container-fluid flex-grow-1 ">
    <h3 class="py-3 mb-4 fw-bold text-white"> @header</h3>
    <div class="card shadow mb-4">
        <div class="card-body">

            <form asp-action="Save" method="post">

                <input asp-for="Id" hidden />

                <div asp-validation-summary="All" class="text-danger"></div>

                <div class="form-group row my-2">
                    <label asp-for="Email" class="col-sm-2 col-form-label">@SharedLocalizer["Email"]</label>
                    <div class="col-md-6">
                        <input asp-for="Email" type="text" class="form-control">
                    </div>
                    <span asp-validation-for="Email" class="text-danger"></span>
                </div>

                <div class="form-group row my-2">
                    <label asp-for="PName" class="col-sm-2 col-form-label">@SharedLocalizer["Name"]</label>
                    <div class="col-md-6">
                        <input asp-for="PName" class="form-control">
                    </div>
                    <span asp-validation-for="PName" class="text-danger"></span>
                </div>

                <div class="form-group row my-2">
                    <label asp-for="Password" class="col-sm-2 col-form-label">@SharedLocalizer["Password"]</label>
                    <div class="col-md-6">
                        <input asp-for="Password" class="form-control">
                        @if (!string.IsNullOrEmpty(Model.Id))
                        {
                            <span>(@SharedLocalizer["ChangePasswordHint"])</span>
                        }
                    </div>
                    <span asp-validation-for="Password" class="text-danger"></span>
                </div>

                <div class="form-group row my-2">
                    <label asp-for="Role" class="col-sm-2 col-form-label">@SharedLocalizer["Role"]</label>
                    <div class="col-md-6">
                        <select asp-for="Role" class="form-control">
                            <option value="Admin">@SharedLocalizer["Admin"]</option>
                            <option value="Moderator">@SharedLocalizer["Moderator"]</option>
                        </select>
                    </div>
                    <span asp-validation-for="Role" class="text-danger"></span>
                </div>

                <div class="form-group row">
                    <div class="col-md-12 col-sm-12">
                        <button type="submit" class=" btn btn-primary my-1 cursor-pointer"><i class="fas fa-save"></i> @SharedLocalizer["Save"]</button>
                        <a asp-action="Index" class="btn btn-secondary my-1"><i class="fas fa-window-close"></i> @SharedLocalizer["Cancel"]</a>
                    </div>
                </div>

            </form>
        </div>
    </div>
</div>


@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}



