﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using TaskDotNet.Localization;
using TaskDotNet.Models;

public class ErrorController : Controller
{
    private readonly IStringLocalizer<SharedResource> _localizer;

    public ErrorController(IStringLocalizer<SharedResource> localizer)
    {
        _localizer = localizer;
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    [Route("Error/{statusCode?}")]
    public IActionResult Index(int? statusCode = null)
    {
        // Default statusCode to 500 if null (meaning unhandled exception)
        statusCode ??= 500;

        // Initialize ErrorViewModel with the status code
        var error = new ErrorViewModel(statusCode.Value, _localizer);

        return View(error);
    }
}
