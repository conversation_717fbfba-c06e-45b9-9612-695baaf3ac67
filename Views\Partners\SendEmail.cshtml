﻿@model Admin.TaskDotNet.Dtos.EmailDto
@{
    var currentLangeCode = System.Globalization.CultureInfo.CurrentCulture.TwoLetterISOLanguageName;
}

@section Links {
    <style>

        .cke_notifications_area {
            display: none;
        }
    </style>
}

<!-- Overlay -->
<div class="content-wrapper">
    <!-- Content -->

    <div class="container-fluid flex-grow-1 ">
        <div class="row justify-content-between align-items-center py-3">
            <div class="col-auto">
                <h3 class="fw-bold text-white">@SharedLocalizer["SendEmailTo"]:</h3>
            </div>
            <div class="col-auto mx-auto">
                <h3 class="fw-bold text-white mb-0">@Model.Name, @Model.Postbox @Model.City</h3>
                <p class="fw-bold text-white h4">@Model.Email</p>
            </div>
            <div class="col-auto">
                <h3 class="fw-bold text-white">@Model.Language</h3>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">

                <div class="card mb-4">
                    <!-- Account -->

                    <hr class="my-0" />
                    <div class="card-body">
                        <div class="row justify-content-center m-3">
                            <div class="mb-3 col-md-12">
                                <label class="form-label" for="EmailText">@SharedLocalizer["Choose email content"]:</label>
                                <select id="EmailText" name="EmailText" class="select2 form-select" asp-items="ViewBag.EmailTexts"><option disabled selected>@SharedLocalizer["Select Template"]</option></select>
                            </div>
                        </div>
                        <form id="myForm" asp-controller="Partners" asp-action="SendEmail" enctype="multipart/form-data">

                            <div class="row justify-content-center m-3 mt-0">
                                <div class="mb-3 col-md-12">
                                    <label class="form-label" asp-for="Title">@SharedLocalizer["Title"]:</label>
                                    <input asp-for="Title" class="form-control" />
                                    <span asp-validation-for="Title" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="row">
                                <input type="hidden" asp-for="Email" />
                                <input type="hidden" asp-for="Name" />
                                <input type="hidden" asp-for="Postbox" />
                                <input type="hidden" asp-for="City" />
                                <input type="hidden" asp-for="Language" />
                                <div class="mb-3 col-md-12">
                                    <textarea asp-for="Message" id="editor"></textarea>
                                </div>
                            </div>
                            <div class="mt-2">
                                <input type="submit" class="btn btn-primary me-2" value="@SharedLocalizer["Send"]" />
                                <a type="reset" class="btn btn-outline-secondary" asp-controller="Partners" asp-action="AllPartners">@SharedLocalizer["Cancel"]</a>
                            </div>
                        </form>
                    </div>
                    <!-- /Account -->
                </div>

            </div>
        </div>
    </div>
    <!-- / Content -->
</div>


@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script src="https://cdn.ckeditor.com/4.22.1/full/ckeditor.js"></script>
    <script>
        $(document).ready(function () {
            // Initialize CKEditor
            CKEDITOR.replace('editor', {
                // CKEditor configuration options
                height: 400,
                language: '@currentLangeCode'
            });


            $("#EmailText").change(function () {
                var id = $("#EmailText").val();
                $.ajax({
                    url: '/EmailText/GetText/' + id,
                    type: 'GET',
                    success: function (data) {
                        // Set the fetched content in the CKEditor
                        CKEDITOR.instances.editor.setData(data.content);
                        $("#Title").val(data.title);
                    },
                    error: function () {
                        alert('Error occurred while fetching text.');
                    }
                });
            });
        });
    </script>
}


