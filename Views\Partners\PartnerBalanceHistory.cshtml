@model IEnumerable<TaskDotNet.Models.BalanceMovement>
@{
    ViewData["Title"] = "Partner Balance History";
}

@section Links {
    <link href="https://cdn.datatables.net/2.0.1/css/dataTables.bootstrap5.css" rel="stylesheet" />
}

<!-- Content wrapper -->
<div class="content-wrapper">
    <!-- Content -->
    <div class="container-fluid flex-grow-1">
        <div class="row py-2 m-0 my-2">
            <h3 class="fw-bold text-white col-md-12">
                <a title="back" class="text-white" asp-action="AllPartners"><i class='bx bxs-left-arrow-circle' style="font-size:2.1rem"></i></a>
                @SharedLocalizer["PartnerBalanceHistory"]: @ViewBag.PartnerName
            </h3>
            <h4 class="fw-bold text-white">@SharedLocalizer["Current Balance"]: CHF @ViewBag.CurrentBalance.ToString("N2")</h4>
        </div>

        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">@SharedLocalizer["BalanceRechargeHistory"]</h5>
            </div>
            <div class="row mx-4">
                <div class="col">
                    <div class="table-responsive text-nowrap">
                        <table id="balanceTable" class="table table-striped" style="width:100%">
                            <thead>
                                <tr>
                                    <th class="text-start">@SharedLocalizer["Date"]</th>
                                    <th class="text-end">@SharedLocalizer["Amount"] (CHF)</th>
                                    <th>@SharedLocalizer["PaymentMethod"]</th>
                                    <th>@SharedLocalizer["Actions"]</th>
                                </tr>
                            </thead>
                            <tbody class="table-border-bottom-0">
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td class="text-start" data-order="@item.CreatedAt.ToString("yyyy-MM-dd")">
                                            @item.CreatedAt.ToString("dd.MM.yyyy")
                                        </td>
                                        <td class="text-end">@item.Amount.ToString("N2")</td>
                                        <td>@item.PaymentMethod</td>
                                        <td>
                                            @if (User.IsInRole("Admin"))
                                            {
                                                <button class="btn btn-danger delete-btn" data-url="@Url.Action("DeleteBalanceRecharge", "Partners", new { id = item.Id, partnerId = ViewBag.PartnerId })">
                                                    <i class='bx bx-trash-alt'></i> @SharedLocalizer["Delete"]
                                                </button>
                                            }
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.datatables.net/2.0.1/js/dataTables.js"></script>
    <script src="https://cdn.datatables.net/2.0.1/js/dataTables.bootstrap5.js"></script>
    <script>
        $(document).ready(function () {
            $('#balanceTable').DataTable({
                order: [[0, 'desc']]
            });
        });
    </script>
}
