﻿@using Admin.TaskDotNet.Dtos
@model ResetOrdersCountDto
@{
    ViewData["Title"] = "Index";
}

<div class="container-fluid flex-grow-1">
    <h3 class="py-3 mb-4 fw-bold text-white">@SharedLocalizer["ResetTitle"]</h3>
    <div class="card shadow mb-4">
        <div class="card-body">

            <form id="resetForm" method="post">
                <!-- Date From -->
                <div class="row mb-3">
                    <label asp-for="DateFrom" class="col-sm-4 col-form-label text-end">
                        @SharedLocalizer["DateFrom"]:
                    </label>
                    <div class="col-sm-6">
                        <input asp-for="DateFrom" class="form-control flat-picker-date bg-white">
                    </div>
                </div>

                <!-- Date To -->
                <div class="row mb-3">
                    <label asp-for="DateTo" class="col-sm-4 col-form-label text-end">@SharedLocalizer["DateTo"]:</label>
                    <div class="col-sm-6">
                        <input asp-for="DateTo" class="form-control flat-picker-date bg-white">
                    </div>
                </div>

                <!-- Reset Dashboard -->
                <div class="row mb-3 align-items-center">
                    <label asp-for="ResetOrdersCount" class="col-sm-4 col-form-label text-end">
                        @SharedLocalizer["ResetDashboard"]:
                    </label>
                    <div class="col-sm-1 form-switch text-center">
                        <input class="form-check-input" asp-for="ResetOrdersCount" style="height:25px; width:60px;" />
                    </div>
                </div>

                <div class="form-group row">
                    <div class="col-md-12 col-sm-12">
                        <button type="button" id="submitButton" class="btn btn-primary my-1 cursor-pointer">
                            <i class="fas fa-save"></i> @SharedLocalizer["Ok"]
                        </button>
                        <a asp-controller="Home" asp-action="Index" class="btn btn-secondary my-1">
                            <i class="fas fa-window-close"></i> @SharedLocalizer["Cancel"]
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        document.getElementById("submitButton").addEventListener("click", function () {
            Swal.fire({
                title: "@SharedLocalizer["AreYouSure"]",
                text: "@SharedLocalizer["DoReset"]",
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#3085d6",
                cancelButtonColor: "#d33",
                confirmButtonText: "@SharedLocalizer["Yes"]",
                cancelButtonText: "@SharedLocalizer["No"]"
            }).then((result) => {
                if (result.isConfirmed) {
                    document.getElementById("resetForm").submit();
                }
            });
        });
    </script>
}
