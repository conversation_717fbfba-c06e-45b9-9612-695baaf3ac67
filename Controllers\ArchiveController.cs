﻿using Admin.TaskDotNet.Dtos;
using Comman.Services.Interfaces;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TaskDotNet.Models;
using TaskDotNet.Comman.DataAccess;

namespace Admin.TaskDotNet.Controllers
{
    [Authorize(Roles = "Admin,Moderator")]
    public class ArchiveController : Controller
    {

        #region Ctor
        private readonly IMapper mapper;
        private readonly IArchiveService archiveService;
        private readonly IActivityService activityService;
        private readonly ApplicationDbContext _dbContext;

        public ArchiveController(IMapper mapper, IArchiveService archiveService, IActivityService activityService, ApplicationDbContext dbContext)
        {
            this.mapper = mapper;
            this.archiveService = archiveService;
            this.activityService = activityService;
            _dbContext = dbContext;
        }
        #endregion

        #region Get All Activity
        public async Task<IActionResult> Index()
        {

            var data = await archiveService.Get();
            var model = mapper.Map<IEnumerable<ArchiveDto>>(data);
            return View(model);

        }
        #endregion

        #region AddToArchive
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> AddToArchive(int id)
        {
            var activity = await _dbContext.Activities.Where(m=>m.Id == id).Include(m=>m.InventoryItems).FirstOrDefaultAsync();
            var archive = mapper.Map<Archive>(activity);
            if (activity != null)
            {
                archive.Id = 0;

                await archiveService.Create(archive);

                _dbContext.ActivityInventoryItems.RemoveRange(activity.InventoryItems);
                activityService.Delete(activity);
                // TempData["ArchiveCreated"] = "done";
                return RedirectToAction("GetAllActivities", "Activities");
            }


            TempData[key: "ErrorMessage"] = "error";
            return RedirectToAction("GetAllActivities", "Activities");
        }

        #endregion

        #region Delete 
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> Delete(int id)
        {
            var data = await archiveService.GetById(id);
            if (data != null)
            {
                //  TempData[key: "ArchiveDeleted"] = "done";
                archiveService.Delete(data);
                return RedirectToAction("Index");

            }
            TempData[key: "ErrorMessage"] = "error";

            return View();
        }


        [HttpPost]
        public IActionResult DeleteSelectedRows([FromBody] List<int> ids)
        {
            if (ids == null || ids.Count == 0)
            {
                return BadRequest(new { message = "No IDs were provided" });
            }

            foreach (var id in ids)
            {
                var entity = _dbContext.Archive.Find(id);
                if (entity != null)
                {
                    _dbContext.Archive.Remove(entity);
                }
            }

            _dbContext.SaveChanges();
            TempData["success"] = $"{ids.Count} record(s) have been deleted.";

            return Json(new { message = "Deleted successfully" });
        }
        #endregion
    }
}
