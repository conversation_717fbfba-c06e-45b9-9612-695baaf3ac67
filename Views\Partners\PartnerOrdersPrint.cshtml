@model IEnumerable<TaskDotNet.Models.PayOrder>
@using Comman.Helper.Extensions
@{
    ViewData["Title"] = "Partner Orders Report";
    Layout = ViewBag.IsPrintView ? "_PrintLayout" : "_Layout";
}

<div class="container mt-4" id="ReportContent">
    <div class="row mb-4">
        <div class="col-12 text-center">
            <h2>@SharedLocalizer["PartnerOrdersReport"]</h2>
            <h4>@ViewBag.PartnerName</h4>
            <h4>@ViewBag.PartnerAddress</h4>
            @if (ViewBag.StartDate != null && ViewBag.EndDate != null)
            {
                <h4>
                    @SharedLocalizer["Period"]: 
                    @(((DateTime)ViewBag.StartDate).ToString("dd.MM.yyyy")) - 
                    @(((DateTime)ViewBag.EndDate).ToString("dd.MM.yyyy"))
                </h4>
            }
            else if (ViewBag.StartDate != null)
            {
                <h4>
                    @SharedLocalizer["From"]: 
                    @(((DateTime)ViewBag.StartDate).ToString("dd.MM.yyyy"))
                </h4>
            }
            else if (ViewBag.EndDate != null)
            {
                <h4>
                    @SharedLocalizer["To"]: 
                    @(((DateTime)ViewBag.EndDate).ToString("dd.MM.yyyy"))
                </h4>
            }
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>@SharedLocalizer["OrderNumber"]</th>
                        <th>@SharedLocalizer["OrderType"]</th>
                        <th>@SharedLocalizer["PurchaseDate"]</th>
                        <th class="text-end">@SharedLocalizer["Price"] (CHF)</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model.OrderByDescending(m => m.PayDate))
                    {
                        <tr>
                            <td>@item.OrderNr</td>
                            <td>@item.ActivityType.GetDisplayName()</td>
                            <td>@item.PayDate.ToString("dd.MM.yyyy")</td>
                            <td class="text-end">@item.Amount.ToString("N2")</td>
                        </tr>
                    }
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="3" class="text-end"><strong>@SharedLocalizer["Total"]:</strong></td>
                        <td class="text-end"><strong>@Model.Sum(m => m.Amount).ToString("N2")</strong></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>

    @if (ViewBag.IsPrintView)
    {
        <div class="row mt-4 no-print">
            <div class="col-12 text-center">
                <button class="btn btn-primary" onclick="window.print()">
                    <i class="fas fa-print"></i> @SharedLocalizer["Print"]
                </button>
                <a class="btn btn-secondary" href="@Url.Action("PartnerOrders", "Partners", new { id = ViewBag.PartnerId })">
                    <i class="fas fa-arrow-left"></i> @SharedLocalizer["Back"]
                </a>
            </div>
        </div>
    }
</div>

@if (ViewBag.IsPrintView)
{
    @section Scripts {
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dompurify/3.0.5/purify.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script>
        // Use jQuery to handle the button click event
        document.addEventListener('DOMContentLoaded', function () {
            // Auto-print when the page loads
            window.print();
        });      
    </script>
    }
}
