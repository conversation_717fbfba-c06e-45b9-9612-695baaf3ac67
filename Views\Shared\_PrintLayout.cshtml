﻿﻿@using Comman.Helper.Extensions
@{
    string currentLanguage = System.Globalization.CultureInfo.CurrentCulture.Name;
}

<!DOCTYPE html>

<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - TaskDotNet</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="~/Dashboard/assets/img/favicon/favicon.ico" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Public+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap"
          rel="stylesheet" />

    <!-- Core CSS -->
    <link rel="stylesheet" href="~/Dashboard/assets/vendor/css/core.css" />
    <link rel="stylesheet" href="~/Dashboard/assets/vendor/css/theme-default.css" />
    <link rel="stylesheet" href="~/Dashboard/assets/css/demo.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />

    <style>
        /* Print-specific styles */
        @@media print {
            body * {
                visibility: hidden !important;
            }

            #ReportContent, #ReportContent * {
                visibility: visible !important;
            }

            #ReportContent {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
            }
            body {
                font-family: 'Public Sans', sans-serif;
                font-size: 14px !important;
                line-height: 1.6;
                color: #333;
            }

            h2, h3 {
                font-weight: 600;
            }
            
            .no-print {
                display: none !important;
            }
            
            .container {
                width: 100%;
                max-width: 100%;
            }
            
            table {
                width: 100%;
                border-collapse: collapse;
            }
            
            th, td {
                padding: 5px !important;
                margin: !important;
                border: 1px solid #ddd;
                font-size: 11pt !important;
            }

        }
    </style>
    
    @await RenderSectionAsync("Links", required: false)
</head>

<body>
    <div class="container-fluid">
        @RenderBody()
    </div>

    <script src="~/Dashboard/assets/vendor/libs/jquery/jquery.js"></script>
    <script src="~/Dashboard/assets/vendor/libs/popper/popper.js"></script>
    <script src="~/Dashboard/assets/vendor/js/bootstrap.js"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
