﻿using Admin.TaskDotNet.Dtos;
using Comman.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TaskDotNet.Comman.DataAccess;
using TaskDotNet.Helper;
using TaskDotNet.Models;

namespace Admin.TaskDotNet.Controllers
{
    [Authorize(Roles = "Admin, Moderator")]

    public class HomeController : Controller
    {
        private readonly ApplicationDbContext _context;

        public HomeController(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IActionResult> IndexAsync()
        {

            var ordersCount = await _context.OrdersCount.FirstOrDefaultAsync(m => m.WebsiteName == "TaskDotNet");

            HomeDto dto = new HomeDto()
            {
                AllActivitiesCount = ordersCount.Total,
                CleaningCount = ordersCount.T_Clean,
                MovingAndCleaningCount = ordersCount.T_Mov_Clean,
                MovingCount = ordersCount.T_Moving,
                PaintingAndGisperCount = ordersCount.T_Paint_Gips,
                WorkersCount = ordersCount.T_Workers,
            };


            return View(dto);
        }
    }


}

