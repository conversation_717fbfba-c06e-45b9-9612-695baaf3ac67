﻿@model Admin.TaskDotNet.Dtos.CompanyDto
@using Microsoft.AspNetCore.Localization

@{
    ViewData["Title"] = "Edit";
}

<div class="content-wrapper">
    <!-- Content -->

    <div class="container-fluid flex-grow-1 ">
        <h3 class="fw-bold text-white py-3 mb-4"><span>@SharedLocalizer["Company"] /</span> @SharedLocalizer["Edit"] @SharedLocalizer["Company Data"]</h3>

        <div class="row">
            <div class="col-md-12">

                <div class="card mb-4">
                    <!-- Account -->

                    <hr class="my-0" />
                    <div class="card-body">
                        <form asp-controller="Company" asp-action="Edit" enctype="multipart/form-data">
                            <div class="row">
                                <input type="hidden" asp-for="Id" />
                                <div asp-validation-summary="All" class="text-danger"> </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Name" class="form-label">@SharedLocalizer["Name"]</label>
                                    <input class="form-control"  asp-for="Name" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Website" class="form-label">@SharedLocalizer["Website"]</label>
                                    <input class="form-control"  asp-for="Website" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Street" class="form-label">@SharedLocalizer["Street"]</label>
                                    <input class="form-control"  asp-for="Street" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="BankName" class="form-label">@SharedLocalizer["Bank Name"]</label>
                                    <input class="form-control"  asp-for="BankName" />
                                </div>
                                <div class="mb-3 col-md-2">
                                    <label asp-for="PostBox" class="form-label">@SharedLocalizer["PostBox"]</label>
                                    <input class="form-control"  asp-for="PostBox" />
                                </div>

                                <div class="mb-3 col-md-4">
                                    <label asp-for="City" class="form-label">@SharedLocalizer["City"]</label>
                                    <input class="form-control"  asp-for="City" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="IBAN_Nr" class="form-label">@SharedLocalizer["IBAN"]</label>
                                    <input class="form-control"  asp-for="IBAN_Nr" />
                                </div>

                                <div class="mb-3 col-md-3">
                                    <label asp-for="Phone" class="form-label">@SharedLocalizer["Phone"]</label>
                                    <input class="form-control"  asp-for="Phone" />
                                </div>
                                <div class="mb-3 col-md-3">
                                    <label asp-for="Mobile" class="form-label">@SharedLocalizer["Mobile"]</label>
                                    <input class="form-control"  asp-for="Mobile" />
                                </div>

                                <div class="mb-3 col-md-3">
                                    <label asp-for="Currency" class="form-label">@SharedLocalizer["Currency"]</label>
                                    <input class="form-control"  asp-for="Currency" />
                                </div>
                                <div class="mb-3 col-md-3">
                                    <label asp-for="Land" class="form-label">@SharedLocalizer["Land"]</label>
                                    <input class="form-control"  asp-for="Land" />
                                </div>

                                <div class="mb-3 col-md-6">
                                    <label asp-for="Email" class="form-label">@SharedLocalizer["Email"]</label>
                                    <input class="form-control"  asp-for="Email" />
                                </div>
                                <div class="mb-3 col-md-6">
                                </div>

                                <div class="mb-3 col-md-6">
                                    <label asp-for="UID" class="form-label">@SharedLocalizer["UID"]</label>
                                    <input class="form-control"  asp-for="UID" />
                                </div>
                                <div class="mb-3 col-md-6">
                                </div>

                                <div class="bg-label-primary my-3">
                                    <hr class="my-0" />
                                    <h5 class="d-flex justify-content-center my-2"> @SharedLocalizer["Activites setup"] </h5>
                                    <hr class="my-0" />
                                </div>

                                <div class="mb-3 col-md-6">
                                    <label asp-for="Price_Move" class="form-label">@SharedLocalizer["Moving"] @SharedLocalizer["Price"]</label>
                                    <input class="form-control"  asp-for="Price_Move" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Offers_Move" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                    <input class="form-control"  asp-for="Offers_Move" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Price_Clean" class="form-label">@SharedLocalizer["Cleaning"] @SharedLocalizer["Price"]</label>
                                    <input class="form-control"  asp-for="Price_Clean" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Offers_Clean" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                    <input class="form-control"  asp-for="Offers_Clean" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Price_MovClean" class="form-label">@SharedLocalizer["Moving and Cleaning"] @SharedLocalizer["Price"]</label>
                                    <input class="form-control"  asp-for="Price_MovClean" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Offers_MovClean" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                    <input class="form-control"  asp-for="Offers_MovClean" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Price_PaintGips" class="form-label">@SharedLocalizer["PaintingAndGisper"] @SharedLocalizer["Price"]</label>
                                    <input class="form-control"  asp-for="Price_PaintGips" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Offers_PaintGips" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                    <input class="form-control"  asp-for="Offers_PaintGips" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Price_Transp" class="form-label">@SharedLocalizer["SmallTransport"] @SharedLocalizer["Price"]</label>
                                    <input class="form-control"  asp-for="Price_Transp" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Offers_Transp" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                    <input class="form-control"  asp-for="Offers_Transp" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Price_Floor" class="form-label">@SharedLocalizer["FloorAndPanels"] @SharedLocalizer["Price"]</label>
                                    <input class="form-control"  asp-for="Price_Floor" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Offers_Floor" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                    <input class="form-control"  asp-for="Offers_Floor" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Price_Kitch" class="form-label">@SharedLocalizer["KitchenConstruction"] @SharedLocalizer["Price"]</label>
                                    <input class="form-control"  asp-for="Price_Kitch" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Offers_Kitch" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                    <input class="form-control"  asp-for="Offers_Kitch" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Price_Elect" class="form-label">@SharedLocalizer["Electrician"] @SharedLocalizer["Price"]</label>
                                    <input class="form-control"  asp-for="Price_Elect" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Offers_Elect" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                    <input class="form-control"  asp-for="Offers_Elect" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Price_Garden" class="form-label">@SharedLocalizer["Garden"] @SharedLocalizer["Price"]</label>
                                    <input class="form-control"  asp-for="Price_Garden" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Offers_Garden" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                    <input class="form-control"  asp-for="Offers_Garden" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Price_Walls" class="form-label">@SharedLocalizer["WallsAndCeilings"] @SharedLocalizer["Price"]</label>
                                    <input class="form-control"  asp-for="Price_Walls" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Offers_Walls" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                    <input class="form-control"  asp-for="Offers_Walls" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Price_Heat" class="form-label">@SharedLocalizer["HeatingAndEnergy"] @SharedLocalizer["Price"]</label>
                                    <input class="form-control"  asp-for="Price_Heat" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Offers_Heat" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                    <input class="form-control"  asp-for="Offers_Heat" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Price_Pluumb" class="form-label">@SharedLocalizer["Plumbing"] @SharedLocalizer["Price"]</label>
                                    <input class="form-control"  asp-for="Price_Pluumb" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Offers_Pluumb" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                    <input class="form-control"  asp-for="Offers_Pluumb" />
                                </div>

                                <div class="mb-3 col-md-6">
                                    <label asp-for="Price_Roofer" class="form-label">@SharedLocalizer["Electrician"] @SharedLocalizer["Price"]</label>
                                    <input class="form-control"  asp-for="Price_Roofer" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Offers_Roofer" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                    <input class="form-control"  asp-for="Offers_Roofer" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Price_Lock" class="form-label">@SharedLocalizer["Locksmith"] @SharedLocalizer["Price"]</label>
                                    <input class="form-control"  asp-for="Price_Lock" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Offers_Lock" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                    <input class="form-control"  asp-for="Offers_Lock" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Price_Welder" class="form-label">@SharedLocalizer["Welder"] @SharedLocalizer["Price"]</label>
                                    <input class="form-control"  asp-for="Price_Welder" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Offers_Welder" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                    <input class="form-control"  asp-for="Offers_Welder" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Price_Refrig" class="form-label">@SharedLocalizer["RefrigerationTechnician"] @SharedLocalizer["Price"]</label>
                                    <input class="form-control"  asp-for="Price_Refrig" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Offers_Refrig" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                    <input class="form-control"  asp-for="Offers_Refrig" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Price_Mechanic" class="form-label">@SharedLocalizer["Mechanic"] @SharedLocalizer["Price"]</label>
                                    <input class="form-control"  asp-for="Price_Mechanic" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Offers_Mechanic" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                    <input class="form-control"  asp-for="Offers_Mechanic" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Price_Individ" class="form-label">@SharedLocalizer["IndividualActivity"] @SharedLocalizer["Price"]</label>
                                    <input class="form-control"  asp-for="Price_Individ" />
                                </div>
                                <div class="mb-3 col-md-6">
                                    <label asp-for="Offers_Individ" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                    <input class="form-control"  asp-for="Offers_Individ" />
                                </div>

                            </div>
                            <div class="mt-2">
                                <input type="submit" class="btn btn-primary me-2" value="@SharedLocalizer["Save"]" />
                                <a asp-controller="Company" asp-action="Index" class="btn btn-outline-secondary">@SharedLocalizer["Cancel"]</a>
                            </div>
                        </form>
                    </div>
                    <!-- /Account -->
                </div>

            </div>
        </div>
    </div>
    <!-- / Content -->
</div>


@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}