﻿@using Admin.TaskDotNet.Dtos
@model Admin.TaskDotNet.Dtos.InventoryItemHelperDto

@{
    ViewData["Title"] = "CreateInventoryItemHelper";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="content-wrapper">
    <!-- Content -->

    <div class="container-fluid flex-grow-1 ">
        <h3 class="fw-bold text-white py-3 mb-4"><span> </span>@SharedLocalizer["Create room content"] </h3>

        <div class="row">
            <div class="col-md-12">

                <div class="card mb-4">
                    <!-- Account -->

                    <hr class="my-0" />
                    <div class="card-body">
                        <form asp-controller="InventoryItems" asp-action="Create" enctype="multipart/form-data">
                            <div class="row align-items-start">

                                <div class="col-md-6 row">
                                    <div class="mb-3 col-md-12">
                                        <label style="font-weight: 800;font-size: larger;" asp-for="InventoryId" class="form-label" for="City">@SharedLocalizer["Category (room)"] </label>
                                        <select asp-for="InventoryId" class="custom-select2 form-control" style="width: 100%; height: 38px;">
                                            @foreach (InventoryDto item in ViewBag.Inventory)
                                            {
                                                <option value="@item.Id">@item.Name</option>
                                            }

                                        </select>
                                    </div>

                                    <div class="mb-3 col-md-12">
                                        <label for="Name" class="form-label">@SharedLocalizer["Name in German"]</label>
                                        <input class="form-control"
                                               type="text"
                                               asp-for="GermanName" />
                                    </div>


                                    <div class="mb-3 col-md-12">
                                        <label for="Name" class="form-label">@SharedLocalizer["Name in English"]</label>
                                        <input class="form-control"
                                               type="text"
                                               asp-for="EnglishName" />
                                    </div>


                                    <div class="mb-3 col-md-12">
                                        <label for="Name" class="form-label">@SharedLocalizer["Name in Italian"] </label>
                                        <input class="form-control"
                                               type="text"
                                               asp-for="ItalianName" />
                                    </div>


                                    <div class="mb-3 col-md-12">
                                        <label for="Name" class="form-label">@SharedLocalizer["Name in French"] </label>
                                        <input class="form-control"
                                               type="text"
                                               asp-for="FrenchName" />
                                    </div>
                                </div>

                                <div class="col-md-6 row">
                                    <div class="mb-3 col-md-6">
                                        <label for="TotalSize" class="form-label">@SharedLocalizer["Unit"] </label>
                                        <input class="form-control"
                                               type="text"
                                               asp-for="Unit" />
                                    </div>

                                    <div class="mb-3 col-md-6">
                                        <label for="TotalSize" class="form-label">@SharedLocalizer["Volume in m3"]</label>
                                        <input class="form-control"
                                               type="text"
                                               asp-for="M3" />

                                    </div>

                                    <div class="mb-3 col-md-12">
                                        <label for="Photo" class="form-label">@SharedLocalizer["Photo"]</label>
                                        <input onchange="changeImage()"  class="form-control" type="file" asp-for="Photo" />
                                    </div>

                                    <div class="col-md-6 text-center">
                                        <img id="ImageUrl" style="width:150px;aspect-ratio:1/1;" />
                                    </div>


                                </div>
                            </div>
                            <div class="mt-2">
                                <input type="submit" class="btn btn-primary me-2" value="@SharedLocalizer["Save"]" />
                                <a asp-controller="InventoryItems" asp-action="index" class="btn btn-outline-secondary"> @SharedLocalizer["Cancel"]</a>
                            </div>
                        </form>
                    </div>
                    <!-- /Account -->
                </div>

            </div>
        </div>
    </div>
    <!-- / Content -->
</div>
@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        function changeImage() {
            document.getElementById("ImageUrl").src = window.URL.createObjectURL(document.getElementById("Photo").files[0]);
        }

    </script>


    @if (TempData["ErrorMessage"] != null)
    {
        <script>
            Swal.fire({
                icon: "error",
                title: "Error...",
                text: "Something went wrong!"

            });
        </script>
    }

}