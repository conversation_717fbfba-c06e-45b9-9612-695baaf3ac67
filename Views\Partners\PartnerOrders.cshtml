@model IEnumerable<TaskDotNet.Models.PayOrder>
@using Comman.Helper.Extensions
@{
    ViewData["Title"] = "Partner Orders";
}

@section Links {
    <link href="https://cdn.datatables.net/2.0.1/css/dataTables.bootstrap5.css" rel="stylesheet" />
    <style>
        .date-filter {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
        }

            .date-filter label {
                margin-bottom: 0;
                white-space: nowrap;
            }

            .date-filter input {
                width: 150px;
            }

        .print-btn {
            margin-left: auto;
        }
    </style>
}

<!-- Content wrapper -->
<div class="content-wrapper">
    <!-- Content -->
    <div class="container-fluid flex-grow-1">
        <div class="row py-2 m-0 my-2">
            <h3 class="fw-bold text-white col-md-12">
                <a title="back" class="text-white" asp-action="AllPartners"><i class='bx bxs-left-arrow-circle' style="font-size:2.1rem"></i></a>
                @SharedLocalizer["PartnerOrders"]: @ViewBag.PartnerName
            </h3>
        </div>

        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">@SharedLocalizer["OrdersList"]</h5>

                <form id="dateFilterForm" method="get" action="@Url.Action("PrintOrdersReport", "Partners")" class="date-filter">
                    <input type="hidden" name="id" value="@ViewBag.PartnerId" />

                    <label for="startDate">@SharedLocalizer["From"]:</label>
                    <input type="date" id="startDate" name="startDate" class="form-control flat-picker-date bg-white" />

                    <label for="endDate">@SharedLocalizer["To"]:</label>
                    <input type="date" id="endDate" name="endDate" class="form-control flat-picker-date bg-white" />

                    <button type="submit" class="btn btn-primary print-btn">
                        <i class="fas fa-print"></i> @SharedLocalizer["PrintReport"]
                    </button>
                </form>
            </div>
            <div class="row mx-4">
                <div class="col">
                    <div class="table-responsive text-nowrap">
                        <table id="ordersTable" class="table table-striped" style="width:100%">
                            <thead>
                                <tr>
                                    <th>@SharedLocalizer["OrderNumber"]</th>
                                    <th>@SharedLocalizer["OrderType"]</th>
                                    <th class="text-start">@SharedLocalizer["PurchaseDate"]</th>
                                    <th>@SharedLocalizer["Price"]</th>
                                    <th>@SharedLocalizer["Actions"]</th>
                                </tr>
                            </thead>
                            <tbody class="table-border-bottom-0">
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td><strong>@item.OrderNr</strong></td>
                                        <td>@item.ActivityType.GetDisplayName()</td>
                                        <td class="text-start" data-order="@item.PayDate.ToString("yyyy-MM-dd")">
                                            @item.PayDate.ToString("dd.MM.yyyy")
                                        </td>
                                        <td>CHF @item.Amount.ToString("N2")</td>
                                        <td>
                                            @if (User.IsInRole("Admin"))

                                            {
                                                <button class="btn btn-danger delete-btn" data-url="@Url.Action("DeleteOrder", "Partners", new { id = item.Id, partnerId = ViewBag.PartnerId })">
                                                    <i class='bx bx-trash-alt'></i> @SharedLocalizer["Delete"]
                                                </button>
                                            }
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.datatables.net/2.0.1/js/dataTables.js"></script>
    <script src="https://cdn.datatables.net/2.0.1/js/dataTables.bootstrap5.js"></script>
    <script>
        $(document).ready(function () {
            $('#ordersTable').DataTable({
                order: [[2, 'desc']]
            });
        });
    </script>
}
