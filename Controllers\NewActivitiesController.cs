﻿using Comman.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TaskDotNet.Comman.DataAccess;
using TaskDotNet.Helper;
using TaskDotNet.Models;
using Microsoft.Extensions.Logging;

namespace Admin.TaskDotNet.Controllers
{
    [Authorize(Roles = "Admin,Moderator")]
    public class NewActivitiesController : Controller
    {
        #region Fields and Constructor
        private readonly IActivityService _activityService;
        private readonly ApplicationDbContext _context;
        private readonly IEmailHtmlTemplateService _emailHtmlTemplateService;
        private readonly IMailService _mailService;

        public NewActivitiesController(
            IActivityService activityService,
            ApplicationDbContext context,
            IEmailHtmlTemplateService emailHtmlTemplateService,
            IMailService mailService)
        {
            _activityService = activityService ?? throw new ArgumentNullException(nameof(activityService));
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _emailHtmlTemplateService = emailHtmlTemplateService ?? throw new ArgumentNullException(nameof(emailHtmlTemplateService));
            _mailService = mailService ?? throw new ArgumentNullException(nameof(mailService));
        }
        #endregion

        #region New Activities
        [HttpGet]
        public async Task<IActionResult> Index(CancellationToken cancellationToken = default)
        {
            try
            {
                var newActivities = await _context.Activities
                        .AsNoTracking()
                        .Where(a => !a.IsChecked)
                        .OrderByDescending(a => a.ActivityType == ActivityType.Cleaning ? a.CleaningDate : a.MovingDate)
                        .ToListAsync(cancellationToken);

                return View(newActivities);
            }
            catch (Exception ex)
            {
                TempData["error"] = "An error occurred while loading activities.";
                return View(new List<Activity>());
            }
        }
     
        #endregion

        #region Mark Activity as Checked
        [HttpPost]
        [Authorize(Roles = "Admin")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> MarkAsChecked(int id, CancellationToken cancellationToken = default)
        {
            if (id <= 0)
            {
                TempData["error"] = "Invalid activity ID.";
                return RedirectToAction("Index");
            }

            try
            {
                using var transaction = await _context.Database.BeginTransactionAsync(cancellationToken);

                var activity = await GetActivityByIdAsync(id, cancellationToken);
                if (activity == null)
                {
   
                    TempData["error"] = "Activity not found.";
                    return RedirectToAction("Index");
                }

                await MarkActivityAsCheckedAsync(activity, cancellationToken);
                await NotifyPartnersAsync(activity, cancellationToken);

                await transaction.CommitAsync(cancellationToken);

                TempData["success"] = "Activity has been processed and partners have been notified.";
            }
            catch (Exception ex)
            {
                TempData["error"] = "An error occurred while processing the activity.";
            }

            return RedirectToAction("Index");
        }

        private async Task<Activity?> GetActivityByIdAsync(int id, CancellationToken cancellationToken)
        {
            return await _context.Activities
                .FirstOrDefaultAsync(a => a.Id == id, cancellationToken);
        }

        private async Task MarkActivityAsCheckedAsync(Activity activity, CancellationToken cancellationToken)
        {
            activity.IsChecked = true;
            await _context.SaveChangesAsync(cancellationToken);
        }

        private async Task NotifyPartnersAsync(Activity activity, CancellationToken cancellationToken)
        {
            var partners = await _activityService.GetPartnersByCountryAndActivity(activity.Kanton, activity.ActivityType);

            if (!partners.Any())
            {
                return;
            }

            var emailTasks = partners.Select(partner => SendPartnerEmailAsync(partner, activity, cancellationToken));
            var results = await Task.WhenAll(emailTasks);
        }

        private async Task<bool> SendPartnerEmailAsync(Partner partner, Activity activity, CancellationToken cancellationToken)
        {
            try
            {
                var lang = MapLanguageCode(partner.Language);
                var emailBody = await _emailHtmlTemplateService.GetActivityEmailTemplate(partner.CompanyName, activity, lang);

                var mailRequest = new MailRequest
                {
                    ToEmail = partner.Email,
                    Subject = "TaskDotNet - New Order",
                    Body = emailBody
                };

                await _mailService.SendEmailAsync(mailRequest, cancellationToken);
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        private static string MapLanguageCode(string? language)
        {
            if (string.IsNullOrWhiteSpace(language))
                return "en";

            return Enum.TryParse<Language>(language, true, out var parsedLanguage)
                ? parsedLanguage switch
                {
                    Language.French => "fr",
                    Language.German => "de",
                    Language.Italian => "it",
                    _ => "en"
                }
                : "en";
        }

        #endregion

        #region Delete Activity
        [HttpPost]
        [Authorize(Roles = "Admin")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteActivity(int id, CancellationToken cancellationToken = default)
        {
            if (id <= 0)
            {
                TempData["error"] = "Invalid activity ID.";
                return RedirectToAction("Index");
            }

            try
            {
                using var transaction = await _context.Database.BeginTransactionAsync(cancellationToken);

                var deleted = await DeleteActivityWithInventoryAsync(id, cancellationToken);

                if (deleted)
                {
                    await transaction.CommitAsync(cancellationToken);
   
                    TempData["success"] = "Activity has been deleted successfully.";
                }
                else
                {
   
                    TempData["error"] = "Activity not found.";
                }
            }
            catch (Exception ex)
            {
                TempData["error"] = "An error occurred while deleting the activity.";
            }

            return RedirectToAction("Index");
        }

        private async Task<bool> DeleteActivityWithInventoryAsync(int id, CancellationToken cancellationToken)
        {
            var activity = await _context.Activities
                .Include(a => a.InventoryItems)
                .FirstOrDefaultAsync(a => a.Id == id, cancellationToken);

            if (activity == null)
                return false;

            // explicitly remove inventory items
            if (activity.InventoryItems?.Any() == true)
            {
                _context.ActivityInventoryItems.RemoveRange(activity.InventoryItems);
            }

            _context.Activities.Remove(activity);
            await _context.SaveChangesAsync(cancellationToken);

            return true;
        }
        #endregion
    }
}