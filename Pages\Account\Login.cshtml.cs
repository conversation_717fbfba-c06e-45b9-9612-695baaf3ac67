﻿// Licensed to the .NET Foundation under one or more agreements.
// The .NET Foundation licenses this file to you under the MIT license.
#nullable disable

using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.ComponentModel.DataAnnotations;
using TaskDotNet.Models;
using TaskDotNet.Services.Auth;

namespace TaskDotNet.Areas.Identity.Pages.Account
{
    public class LoginModel : PageModel
    {
        private readonly IAuthService _authService;
        private readonly ILogger<LoginModel> _logger;

        public LoginModel(ILogger<LoginModel> logger, IAuthService authService)
        {

            _logger = logger;
            _authService = authService;
        }

        [BindProperty]
        public InputModel Input { get; set; }

        public string ReturnUrl { get; set; }

        [TempData]
        public string ErrorMessage { get; set; }

        public class InputModel
        {

            [Required]
            [EmailAddress]
            public string Email { get; set; }

            [Required]
            [DataType(DataType.Password)]
            public string Password { get; set; }

            [Display(Name = "Remember me?")]
            public bool RememberMe { get; set; }
        }

        public async Task OnGetAsync(string returnUrl = null)
        {
            if (!string.IsNullOrEmpty(ErrorMessage))
            {
                ModelState.AddModelError(string.Empty, ErrorMessage);
            }

            returnUrl ??= Url.Content("~/");

            ReturnUrl = returnUrl;
        }

        public async Task<IActionResult> OnPostAsync(string returnUrl = null)
        {
            // Set default returnUrl if not provided
            returnUrl ??= Url.Content("~/");

            // Validate the model state before attempting login
            if (!ModelState.IsValid)
            {
                return Page();
            }

            // Attempt to sign in the user
            
            var result = await _authService.LoginAsync(Input.Email, Input.Password, Input.RememberMe);

            if (result)
            {
                _logger.LogInformation("User '{Email}' logged in successfully.", Input?.Email);
                return LocalRedirect(returnUrl);
            }

            // Handle invalid login attempt (wrong password, etc.)
            _logger.LogWarning("Invalid login attempt for user '{Email}'.", Input?.Email);
            ModelState.AddModelError(string.Empty, "Invalid login attempt.");

            return Page();
        }

    }
}
