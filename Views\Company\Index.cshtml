﻿@model Admin.TaskDotNet.Dtos.CompanyDto
@{
    ViewData["Title"] = "Index";
}

<div class="content-wrapper">
    <!-- Content -->

    <div class="container-fluid flex-grow-1 ">
        <h3 class="fw-bold text-white py-3 mb-4">
            <span>
                @SharedLocalizer["Company"] /
            </span>  @SharedLocalizer["CompanyDetails"]
        </h3>

        <div class="row">
            <div class="col-md-12">

                <div class="card mb-4">
                    <!-- Account -->

                    <hr class="my-0" />
                    <div class="card-body">
                        <div class="row">
                            <div class="mb-3">
                                <a asp-controller="Company" asp-action="Edit" class="btn btn-primary">@SharedLocalizer["Update"]</a>
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Name" class="form-label">@SharedLocalizer["Name"]</label>
                                <input class="form-control" readonly asp-for="Name" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Website" class="form-label">@SharedLocalizer["Website"]</label>
                                <input class="form-control" readonly asp-for="Website" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Street" class="form-label">@SharedLocalizer["Street"]</label>
                                <input class="form-control" readonly asp-for="Street" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="BankName" class="form-label">@SharedLocalizer["Bank Name"]</label>
                                <input class="form-control" readonly asp-for="BankName" />
                            </div>
                            <div class="mb-3 col-md-2">
                                <label asp-for="PostBox" class="form-label">@SharedLocalizer["PostBox"]</label>
                                <input class="form-control" readonly asp-for="PostBox" />
                            </div>

                            <div class="mb-3 col-md-4">
                                <label asp-for="City" class="form-label">@SharedLocalizer["City"]</label>
                                <input class="form-control" readonly asp-for="City" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="IBAN_Nr" class="form-label">@SharedLocalizer["IBAN"]</label>
                                <input class="form-control" readonly asp-for="IBAN_Nr" />
                            </div>

                            <div class="mb-3 col-md-3">
                                <label asp-for="Phone" class="form-label">@SharedLocalizer["Phone"]</label>
                                <input class="form-control" readonly asp-for="Phone" />
                            </div>
                            <div class="mb-3 col-md-3">
                                <label asp-for="Mobile" class="form-label">@SharedLocalizer["Mobile"]</label>
                                <input class="form-control" readonly asp-for="Mobile" />
                            </div>

                            <div class="mb-3 col-md-3">
                                <label asp-for="Currency" class="form-label">@SharedLocalizer["Currency"]</label>
                                <input class="form-control" readonly asp-for="Currency" />
                            </div>
                            <div class="mb-3 col-md-3">
                                <label asp-for="Land" class="form-label">@SharedLocalizer["Land"]</label>
                                <input class="form-control" readonly asp-for="Land" />
                            </div>

                            <div class="mb-3 col-md-6">
                                <label asp-for="Email" class="form-label">@SharedLocalizer["Email"]</label>
                                <input class="form-control" readonly asp-for="Email" />
                            </div>
                            <div class="mb-3 col-md-6">
                            </div>

                            <div class="mb-3 col-md-6">
                                <label asp-for="UID" class="form-label">@SharedLocalizer["UID"]</label>
                                <input class="form-control" readonly asp-for="UID" />
                            </div>
                            <div class="mb-3 col-md-6">
                            </div>

                            <div class="bg-label-primary my-3">
                                <hr class="my-0" />
                                <h5 class="d-flex justify-content-center my-2" style="color:aliceblue; font-weight:700;"> @SharedLocalizer["Activites setup"] </h5>
                                <hr class="my-0" />
                            </div>

                            <div class="mb-3 col-md-6">
                                <label asp-for="Price_Move" class="form-label">@SharedLocalizer["Preisfuer"] @SharedLocalizer["Moving"]</label>
                                <input class="form-control" readonly asp-for="Price_Move" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Offers_Move" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                <input class="form-control" readonly asp-for="Offers_Move" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Price_Clean" class="form-label">@SharedLocalizer["Preisfuer"] @SharedLocalizer["Cleaning"]</label>
                                <input class="form-control" readonly asp-for="Price_Clean" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Offers_Clean" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                <input class="form-control" readonly asp-for="Offers_Clean" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Price_MovClean" class="form-label">@SharedLocalizer["Preisfuer"] @SharedLocalizer["Moving and Cleaning"]</label>
                                <input class="form-control" readonly asp-for="Price_MovClean" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Offers_MovClean" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                <input class="form-control" readonly asp-for="Offers_MovClean" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Price_PaintGips" class="form-label">@SharedLocalizer["Preisfuer"] @SharedLocalizer["PaintingAndGisper"]</label>
                                <input class="form-control" readonly asp-for="Price_PaintGips" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Offers_PaintGips" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                <input class="form-control" readonly asp-for="Offers_PaintGips" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Price_Transp" class="form-label">@SharedLocalizer["Preisfuer"] @SharedLocalizer["SmallTransport"]</label>
                                <input class="form-control" readonly asp-for="Price_Transp" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Offers_Transp" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                <input class="form-control" readonly asp-for="Offers_Transp" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Price_Floor" class="form-label">@SharedLocalizer["Preisfuer"] @SharedLocalizer["FloorAndPanels"]</label>
                                <input class="form-control" readonly asp-for="Price_Floor" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Offers_Floor" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                <input class="form-control" readonly asp-for="Offers_Floor" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Price_Kitch" class="form-label">@SharedLocalizer["Preisfuer"] @SharedLocalizer["KitchenConstruction"]</label>
                                <input class="form-control" readonly asp-for="Price_Kitch" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Offers_Kitch" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                <input class="form-control" readonly asp-for="Offers_Kitch" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Price_Elect" class="form-label">@SharedLocalizer["Preisfuer"] @SharedLocalizer["Roofer"]</label>
                                <input class="form-control" readonly asp-for="Price_Elect" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Offers_Elect" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                <input class="form-control" readonly asp-for="Offers_Elect" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Price_Garden" class="form-label">@SharedLocalizer["Preisfuer"] @SharedLocalizer["Garden"]</label>
                                <input class="form-control" readonly asp-for="Price_Garden" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Offers_Garden" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                <input class="form-control" readonly asp-for="Offers_Garden" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Price_Walls" class="form-label">@SharedLocalizer["Preisfuer"] @SharedLocalizer["WallsAndCeilings"]</label>
                                <input class="form-control" readonly asp-for="Price_Walls" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Offers_Walls" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                <input class="form-control" readonly asp-for="Offers_Walls" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Price_Heat" class="form-label">@SharedLocalizer["Preisfuer"] @SharedLocalizer["HeatingAndEnergy"]</label>
                                <input class="form-control" readonly asp-for="Price_Heat" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Offers_Heat" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                <input class="form-control" readonly asp-for="Offers_Heat" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Price_Pluumb" class="form-label">@SharedLocalizer["Preisfuer"] @SharedLocalizer["Plumbing"]</label>
                                <input class="form-control" readonly asp-for="Price_Pluumb" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Offers_Pluumb" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                <input class="form-control" readonly asp-for="Offers_Pluumb" />
                            </div>
                           
                            <div class="mb-3 col-md-6">
                                <label asp-for="Price_Roofer" class="form-label">@SharedLocalizer["Preisfuer"] @SharedLocalizer["Electrician"]</label>
                                <input class="form-control" readonly asp-for="Price_Roofer" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Offers_Roofer" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                <input class="form-control" readonly asp-for="Offers_Roofer" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Price_Lock" class="form-label">@SharedLocalizer["Preisfuer"] @SharedLocalizer["Locksmith"]</label>
                                <input class="form-control" readonly asp-for="Price_Lock" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Offers_Lock" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                <input class="form-control" readonly asp-for="Offers_Lock" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Price_Welder" class="form-label">@SharedLocalizer["Preisfuer"] @SharedLocalizer["Welder"]</label>
                                <input class="form-control" readonly asp-for="Price_Welder" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Offers_Welder" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                <input class="form-control" readonly asp-for="Offers_Welder" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Price_Refrig" class="form-label">@SharedLocalizer["Preisfuer"] @SharedLocalizer["RefrigerationTechnician"]</label>
                                <input class="form-control" readonly asp-for="Price_Refrig" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Offers_Refrig" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                <input class="form-control" readonly asp-for="Offers_Refrig" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Price_Mechanic" class="form-label">@SharedLocalizer["Preisfuer"] @SharedLocalizer["Mechanic"]</label>
                                <input class="form-control" readonly asp-for="Price_Mechanic" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Offers_Mechanic" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                <input class="form-control" readonly asp-for="Offers_Mechanic" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Price_Individ" class="form-label">@SharedLocalizer["Preisfuer"] @SharedLocalizer["IndividualActivity"]</label>
                                <input class="form-control" readonly asp-for="Price_Individ" />
                            </div>
                            <div class="mb-3 col-md-6">
                                <label asp-for="Offers_Individ" class="form-label">@SharedLocalizer["Maximum offers"]</label>
                                <input class="form-control" readonly asp-for="Offers_Individ" />
                            </div>

                        </div>
                        <!-- /Account -->
                    </div>

                </div>
            </div>
        </div>
        <!-- / Content -->
    </div>
</div>



@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>




    @if (TempData["CompanyUpdated"] != null)



    {
        <script>
            Swal.fire({
                title: "Updated Successfully !",
                text: "Company data has been updated successfully",
                icon: "success",
            });
        </script>

    }

    @if (TempData["ErrorMessage"] != null)



    {
        <script>
            Swal.fire({
                icon: "error",
                title: "Error...",
                text: "Something went wrong!"

            });
        </script>
    }
}