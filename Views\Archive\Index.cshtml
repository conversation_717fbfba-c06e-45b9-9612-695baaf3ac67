﻿@using Comman.Helper.Extensions
@using TaskDotNet.Helper
@model IEnumerable<Admin.TaskDotNet.Dtos.ArchiveDto>

@{
    ViewData["Title"] = "Index";

    var confirmDeleteTitle = @SharedLocalizer["Delete record"];
    var confirmDeleteText2 = @SharedLocalizer["Are you sure you want to delete the selected entry?"];


    var cleaningText = @SharedLocalizer["Cleaning"];
    var movingText = @SharedLocalizer["Moving"];
    var movingandcleaningText = @SharedLocalizer["Move And Clean"];
    var painting = @SharedLocalizer["Painting"];

    var cancelButtonText = @SharedLocalizer["Cancel"];
    var yesText = @SharedLocalizer["Yes"];
}
<script>
    // Pass the translated text to JavaScript variables
    var cleaningText = '@cleaningText';
    var movingText = '@movingText';
    var movingandcleaningText = '@movingandcleaningText';
    var paintingText = '@painting';
</script>

@section Links {
    <link href="https://cdn.datatables.net/2.0.1/css/dataTables.bootstrap5.css" rel="stylesheet" />
    <style>
        .dt-search {
            width: 250px;
        }

    </style>
}

<!-- Content wrapper -->
<div class="content-wrapper">
    <!-- Content -->
    <div class="container-fluid flex-grow-1">
        <h3 class="fw-bold text-white py-3 mb-4">@SharedLocalizer["Archive Content"]</h3>

        <!-- Basic Bootstrap Table -->
        <div class="card">
            <div class="text-nowrap mx-4">
                <div class="row justify-content-end m-3">
                    <button id="deleteSelected" class="btn btn-danger" style="display: none;">@SharedLocalizer["Delete Selected"]</button>
                </div>
                <div class="row">
                    <div class="col">
                        <div class="table-responsive">
                            <table id="example" class="table text-center" style="width:100%">
                                <thead>
                                    <tr>
                                        @if (User.IsInRole("Admin"))
                                        {
                                            <th class="no-sort"><input type="checkbox" class="form-check-input" id="selectAll"></th>
                                        }
                                        <th class="text-center">@SharedLocalizer["Activity"]</th>
                                        <th class="text-center">@SharedLocalizer["Exec. Date"]</th>
                                        <th class="text-center">@SharedLocalizer["Name"]</th>
                                        <th class="bg-body text-center">@SharedLocalizer["BoxCity"]</th>
                                        <th class="bg-body text-center">Kanton</th>
                                        <th class="text-center">@SharedLocalizer["Object"]</th>
                                    </tr>
                                </thead>
                                <tbody class="table-border-bottom-0">
                                    @foreach (var item in Model)

                                    {

                                        <tr>
                                            @if (User.IsInRole("Admin"))
                                            {
                                                <td><input type="checkbox" class="rowCheckbox form-check-input" data-id="@item.Id"></td>
                                            }
                                            <td class="text-lg-start">
                                                <img src="~/Dashboard/assets/img/@(item.ActivityType == ActivityType.Moving ? "moving.png" : item.ActivityType == ActivityType.Cleaning ? "cleaning.png" : item.ActivityType == ActivityType.MovingAndCleaning ? "moving and cleaning.png" : item.ActivityType == ActivityType.PaintingAndGisper ? "painting.png" : "gisper.png")" style="height:30px;padding-right: 5px;" />
                                                <span style="vertical-align: bottom;" class="fs-4">@(item.ActivityType == ActivityType.Moving ? movingText : item.ActivityType == ActivityType.Cleaning ? cleaningText : item.ActivityType == ActivityType.MovingAndCleaning ? movingandcleaningText : item.ActivityType == ActivityType.PaintingAndGisper ? painting : $"Activity ({item.ActivityType.GetDisplayName()})")</span>
                                            </td>
                                            <td>
                                                @(item.ActivityType == ActivityType.Moving ? item.MovingDate?.ToString("dd.MM.yyyy") : item.ActivityType == ActivityType.Cleaning ? item.CleaningDate?.ToString("dd.MM.yyyy") : item.ActivityType == ActivityType.MovingAndCleaning ? item.MovingDate?.ToString("dd.MM.yyyy") : item.ActivityType == ActivityType.PaintingAndGisper ? item.MovingDate?.ToString("dd.MM.yyyy") : item.MovingDate?.ToString("dd.MM.yyyy"))
                                            </td>
                                            <td>@item.Name</td>
                                            <td class="@(item.ActivityType == ActivityType.Moving || item.ActivityType == ActivityType.MovingAndCleaning ? "bg-body" : "")">@item.PostBox @item.City</td>

                                            <td>@item.Kanton</td>

                                            <td>@item.Object</td>
                                        </tr>

                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--/ Basic Bootstrap Table -->
    </div>
</div>

@section Scripts {
    <script src="https://cdn.datatables.net/2.0.1/js/dataTables.js"></script>
    <script src="https://cdn.datatables.net/2.0.1/js/dataTables.bootstrap5.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>


    <script>
        $(document).ready(function () {
            var dataTable = $('#example').DataTable({ "columnDefs": [ { "targets": 'no-sort', "orderable": false, } ],"order": [[2, 'desc']] });

                 // Function to toggle the visibility of the "Delete Selected" button
                function toggleDeleteButton() {
                    if ($('.rowCheckbox:checked').length > 0) {
                        $('#deleteSelected').show(); // Show the button when rows are selected
                    } else {
                        $('#deleteSelected').hide(); // Hide the button when no rows are selected
                    }
                }

                // Handle "Select All" checkbox
                $('#selectAll').on('click', function() {
                    $('.rowCheckbox').prop('checked', this.checked);
                    toggleDeleteButton();
                });

                // Handle individual row checkboxes
                $('.rowCheckbox').on('click', function() {
                    toggleDeleteButton();
                });

                // Handle "Delete Selected" button

                $('#deleteSelected').on('click', function() {
                    var selectedIds = [];

                    // Get all selected row IDs
                    $('.rowCheckbox:checked').each(function() {
                        var id = $(this).attr('data-id'); // Retrieve the data-id attribute explicitly
                        if (id) {
                            selectedIds.push(id); // Add the id to the array
                        }
                    });

                    var recordCount = selectedIds.length; // Get the number of selected records

                    if (recordCount === 0) {
                        alert('No rows selected');
                        return;
                    }

                    // SweetAlert confirmation dialog with dynamic record count
                    Swal.fire({
                        title: '@confirmDeleteTitle',
                        text: '@Html.Raw(confirmDeleteText2)',
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#3085d6',
                        cancelButtonColor: '#d33',
                        confirmButtonText: '@yesText',
                        cancelButtonText: '@cancelButtonText'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // User confirmed the deletion, proceed with the AJAX request
                            $.ajax({
                                url: '/Archive/DeleteSelectedRows',
                                type: 'POST',
                                data: JSON.stringify(selectedIds), // Send selected IDs to server
                                contentType: 'application/json; charset=utf-8',
                                success: function(response) {
                                    location.reload(true);
                                },
                                error: function(error) {
                                    Swal.fire('Error!', 'There was an error deleting the rows.', 'error');
                                }
                            });
                        }
                    });
                });
        });
    </script>
}
