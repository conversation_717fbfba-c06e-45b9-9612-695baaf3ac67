﻿@model IEnumerable<ContactUs>

@{
    ViewData["Title"] = "Index";
}


@section Links {
    <link src="https://cdn.datatables.net/2.0.1/css/dataTables.bootstrap5.css" />
}



<!-- Content wrapper -->
<div class="content-wrapper">
    <!-- Content -->-

    <div class="container-fluid flex-grow-1 ">
        <h3 class="fw-bold text-white py-3 mb-4"><span></span>@SharedLocalizer["ContactUs"]</h3>

        <!-- Basic Bootstrap Table -->
        <div class="card">

            <div class="table-responsive text-nowrap container">
                <table id="example" class="table table-striped" style="width:100%">
                    <thead>
                        <tr>
                            <th>@SharedLocalizer["Name"]</th>
                            <th>@SharedLocalizer["Email"]</th>
                            <th>@SharedLocalizer["Phone"]</th>
                            <th>@SharedLocalizer["Message"]</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody class="table-border-bottom-0">
                        @foreach (var contact in Model)



                        {
                            <tr>
                                <td>@contact.Name</td>
                                <td>@contact.Email</td>
                                <td>@contact.PhoneNumber</td>
                                <td>@(contact.Message != null && contact.Message.Length > 50 ? contact.Message.Substring(0, 50) + "..." : contact.Message)</td>
                                <td class="text-center mt-3">
                                    <a href="@Url.Action("Details", "ContactUs", new { id = contact.Id })" class="btn btn-warning me-2">@SharedLocalizer["Viewing"]</a>
                                    @if (User.IsInRole("Admin"))
                                    {
                                        <button class="btn btn-danger delete-btn" data-url="@Url.Action("Delete", "ContactUs", new { id = contact.Id })">
                                            @SharedLocalizer["Delete"]
                                        </button>
                                    }
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
        <!--/ Basic Bootstrap Table -->

    </div>
</div>




@section Scripts {
    <script src="https://cdn.datatables.net/2.0.1/js/dataTables.js"></script>
    <script src="https://cdn.datatables.net/2.0.1/js/dataTables.bootstrap5.js"></script>


    <script>
        $(document).ready(function () {
            new DataTable('#example');
        });
    </script>
}