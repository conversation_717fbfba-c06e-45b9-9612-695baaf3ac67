﻿@model Admin.TaskDotNet.Dtos.ActivityDto

@{
    ViewData["Title"] = SharedLocalizer["ActivityDetails"];
}

<div class="content-wrapper">
    <!-- Content -->

    <div class="container-fluid flex-grow-1 ">
        <h3 class="fw-bold text-white py-3 mb-4"><a title="back" class="text-white" asp-action="GetAllActivities"><i class='bx bxs-left-arrow-circle' style="font-size:2.1rem"></i></a>  <span>@SharedLocalizer["Activity"] /</span> @SharedLocalizer["ActivityDetails"]</h3>


        <div class="row">
            <div class="col-md-12">

                <div class="card mb-4">

                    <div class="card-body">

                        <div id="ActivityContent" style="max-width: 700px; margin: auto;font-size: 18px;">

                            <!-- header  -->
                            <h3 style="color:#008284;font-family: arial; margin: 0; text-align: center; border: 1px solid #008284">
                                @SharedLocalizer["NewRequest"]: #@Model.OrderNr
                            </h3>
                            <div style="padding-top: 10px">
                                <p style="padding-left: 20px">@SharedLocalizer["Hello"],</p>
                                <p style="padding-left: 20px">
                                    @SharedLocalizer["ThisIsRequiredRequest"]
                                </p>
                                <!-- separator  -->
                                <div style="background-color: #008284; height: 3px"></div>
                                <table style="padding: 5px 0; width: 100%">
                                    <tr>
                                        <td style="width: 25%"></td>
                                        <td style="width: 35%; font-weight: bold">@SharedLocalizer["Category"]:</td>
                                        <td style="width: 35%; color: black;font-weight: bold;">@SharedLocalizer["Moving"]</td>
                                    </tr>
                                    <tr>
                                        <td></td>
                                        <td style="font-weight: bold">@SharedLocalizer["ExecDate"]:</td>
                                        <td style="color: black;font-weight: bold;">@Model.MovingDate.ToString("dd.MM.yyyy")</td>
                                    </tr>
                                </table>

                                <table style="padding: 5px 0; width: 100%">
                                    <tr>
                                        <td style="width: 26.5%"></td>
                                        <td style="font-weight: bold; vertical-align: top">@SharedLocalizer["Customer"]:</td>
                                        <td style="vertical-align: top">
                                            <p style="margin: 0;color: black;font-weight: bold;">
                                                @Model.Name<br />
                                                @Model.Phone<br />
                                                @Model.Email
                                            </p>
                                        </td>
                                    </tr>
                                </table>
                                <!-- separator  -->
                                <div style="background-color: #008284; height: 3px"></div>

                                <table style="margin-bottom: 20px;margin-top:5px;width: 100%;">
                                    <tbody>

                                        <tr style="background-color:#DCD6D6;">
                                            <td style="width: 25%"></td>
                                            <td style="color: black;font-weight: bold;" class="py-1">
                                                @SharedLocalizer["MoveFrom"]:
                                            </td>
                                            <td style="width:10px"></td>
                                            <td style="color: black;font-weight: bold;">
                                                @SharedLocalizer["MoveTo"]:
                                            </td>
                                        </tr>
                                        <tr style="height:5px"></tr>
                                        <tr>
                                            <td style="width: 25%"></td>
                                            <td style="vertical-align: top">
                                                @Model.Street <br />
                                                @Model.PostBox @Model.City
                                            </td>
                                            <td></td>
                                            <td style="vertical-align: top">
                                                @Model.TStreet <br />
                                                @Model.TPostBox @Model.TCity
                                            </td>
                                        </tr>
                                        <tr style="height:10px"></tr>
                                        <tr style="padding: 10px">
                                            <td style="font-weight: bold; vertical-align: top">@SharedLocalizer["Object"]:</td>
                                            <td>
                                                @Model.Room @SharedLocalizer["Room"]-@SharedLocalizer[Model.Object]<br />
                                                @Model.Area m²<br />
                                                @SharedLocalizer[Model.Floor], @SharedLocalizer["Lift"] : @(Model.FLift ?? false ? SharedLocalizer["Yes"] : SharedLocalizer["No"]) <br />
                                                @Model.FDistance m @SharedLocalizer["toTheLoadingEdge"]
                                            </td>
                                            <td></td>
                                            <td>
                                                @SharedLocalizer[Model.TObject] <br />
                                                <br />
                                                @SharedLocalizer[Model.TFloor], @SharedLocalizer["Lift"] : @(Model.TLift ?? false ? SharedLocalizer["Yes"] : SharedLocalizer["No"]) <br />
                                                @Model.TDistance m @SharedLocalizer["toTheDrainEdge"]
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <!-- separator  -->
                                <div style="background-color: #008284; height: 4px; width: 60%; margin: 5px auto 0;"></div>

                                <table style="width: 100%; margin: 20px 0">
                                    <tbody>
                                        <tr>
                                            <td style="font-weight: bold;text-align: center;padding-bottom: 25px;" colspan="5">@SharedLocalizer["AdditionalServices"]:</td>
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td style="font-weight: bold">@SharedLocalizer["DismantleFurniture"]:</td>
                                            <td style="padding: 0 15px">@(Model.FDismantFurnit ?? false ? SharedLocalizer["Yes"] : SharedLocalizer["No"])</td>
                                            <td style="font-weight: bold">@SharedLocalizer["AssembleFurniture"]:</td>
                                            <td>@(Model.TMountMöbel ?? false ? SharedLocalizer["Yes"] : SharedLocalizer["No"])</td>
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td style="font-weight: bold">@SharedLocalizer["DismantleLamp"]:</td>
                                            <td style="padding: 0 15px">@(Model.FDismantLamp ?? false ? SharedLocalizer["Yes"] : SharedLocalizer["No"])</td>
                                            <td style="font-weight: bold">@SharedLocalizer["InstallLamp"]:</td>
                                            <td>@(Model.TmontLamp ?? false ? SharedLocalizer["Yes"] : SharedLocalizer["No"])</td>
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td style="font-weight: bold">@SharedLocalizer["Packing"]:</td>
                                            <td style="padding: 0 15px">@(Model.FWrapUp ?? false ? SharedLocalizer["Yes"] : SharedLocalizer["No"])</td>
                                            <td style="font-weight: bold">@SharedLocalizer["Unpacking"]:</td>
                                            <td>@(Model.TAuspacken ?? false ? SharedLocalizer["Yes"] : SharedLocalizer["No"])</td>
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td style="font-weight: bold">@SharedLocalizer["FurnitureLift"]:</td>
                                            <td style="padding: 0 15px">@(Model.Mobellift ?? false ? SharedLocalizer["Yes"] : SharedLocalizer["No"])</td>
                                            <td style="font-weight: bold">@SharedLocalizer["Storage"]:</td>
                                            <td>@(Model.Lager ?? false ? SharedLocalizer["Yes"] : SharedLocalizer["No"])</td>
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td style="font-weight: bold">@SharedLocalizer["Piano"]:</td>
                                            <td style="padding: 0 15px">@(Model.Klavier ?? false ? SharedLocalizer["Yes"] : SharedLocalizer["No"])</td>
                                            <td style="font-weight: bold">@SharedLocalizer["Disposal"]:</td>
                                            <td>@(Model.Disposal ?? false ? SharedLocalizer["Yes"] : SharedLocalizer["No"])</td>
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td style="font-weight: bold">@SharedLocalizer["HeavyLoad"]:</td>
                                            <td style="padding: 0 15px">@(Model.Schwer ?? false ? SharedLocalizer["Yes"] : SharedLocalizer["No"])</td>
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td style="font-weight: bold">@SharedLocalizer["Cellar"]:</td>
                                            <td style="padding: 0 15px">@(Model.Celler ?? false ? SharedLocalizer["Yes"] : SharedLocalizer["No"])</td>
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td style="font-weight: bold">@SharedLocalizer["Garage"]:</td>
                                            <td style="padding: 0 15px">@(Model.Garage ?? false ? SharedLocalizer["Yes"] : SharedLocalizer["No"])</td>
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td style="font-weight: bold">@SharedLocalizer["MovingBoxes"]:</td>
                                            <td colspan="3" style="padding: 0 15px"> @SharedLocalizer["Approx."] @Model.Kartons @SharedLocalizer["Boxes"] (30kg)</td>
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td style="font-weight: bold">@SharedLocalizer["CustomerFocus"]:</td>
                                            <td colspan="3" style="padding: 0 15px"> @SharedLocalizer[Model.Focus]</td>
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td style="font-weight: bold">@SharedLocalizer["Inspection"]:</td>
                                            <td colspan="3" style="padding: 0 15px;color: black;font-weight: bold;"> @(Model.ViewingDate != DateTime.MinValue ? @SharedLocalizer["YesOn"] + " " + Model.ViewingDate.ToString("dd.MM.yyyy") : @SharedLocalizer["NotRequired"])</td>
                                        </tr>
                                    </tbody>
                                </table>

                                @Html.Raw(ViewBag.InventoryHtml)

                                <div style="padding-top: 10px">
                                    <p><b>@SharedLocalizer["Notes"]:</b> @Model.Notes</p>
                                </div>
                                <!-- separator  -->
                                <div style="background-color: #008284; height: 4px; width: 60%; margin: 5px auto 0;"></div>

                                <p style="padding-top: 10px;font-weight: 800">@SharedLocalizer["Team"]</p>
                            </div>

                        </div>
                        <!-- /Account -->
                    </div>


                </div>
            </div>
        </div>
        <!-- / Content -->
    </div>
</div>
