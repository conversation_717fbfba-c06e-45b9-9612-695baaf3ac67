﻿using Microsoft.AspNetCore.Mvc;
using TaskDotNet.Comman.DataAccess;

public class NotificationsViewComponent : ViewComponent
{
    private readonly ApplicationDbContext _context;
    public NotificationsViewComponent(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<IViewComponentResult> InvokeAsync()
    {
        var count = _context.ActivityNotifications.Count();
        return View(count);
    }
}
