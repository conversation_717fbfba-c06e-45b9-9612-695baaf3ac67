﻿@using Admin.TaskDotNet.Dtos
@model IEnumerable<UserDto>

@{
    ViewBag.Title = "Users";
}

<style>
    .color {
        border-radius: 50%;
        height: 40px;
        width: 40px;
    }
</style>

<!-- Content wrapper -->
<div class="content-wrapper">
    <!-- Content -->

    <div class="container-fluid flex-grow-1 ">
        <h3 class="py-3 mb-4 fw-bold text-white">@SharedLocalizer["UsersList"]</h3>

        <div class="card shadow mb-4">
            <div class="card-header py-3">

                <a class="btn btn-primary" asp-action="Create">
                    <i class="fa fa-plus-circle" aria-hidden="true"></i>
                    @SharedLocalizer["Create entry"]
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered text-dark" id="dataTable" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>Email</th>
                                <th class="d-none d-md-none d-lg-table-cell d-xl-table-cell">@SharedLocalizer["Name"]</th>
                                <th class="d-none d-md-none d-lg-table-cell d-xl-table-cell">@SharedLocalizer["Role"]</th>
                                <th>@SharedLocalizer["Actions"]</th>
                            </tr>
                        </thead>

                        <tbody>
                            @foreach (var item in Model)
                            {
                                <tr>
                                    <td>@item.Email</td>
                                    <td class="d-none d-md-none d-lg-table-cell d-xl-table-cell">
                                        @item.PName
                                    </td>

                                    <td class="d-none d-md-none d-lg-table-cell d-xl-table-cell">
                                        @item.Role
                                    </td>
                                    <td class="align-middle">
                                        <a type="button" class="btn btn-info" asp-controller="Users" asp-action="Update" asp-route-id="@item.Id">@SharedLocalizer["Edit"]</a>
                                        <button class="btn btn-danger delete-btn" data-url="@Url.Action("Delete", "Users", new { id = item.Id })">
                                            @SharedLocalizer["Delete"]
                                        </button>
                                    </td>
                                </tr>
                            }
                        </tbody>

                    </table>
                </div>
            </div>
        </div>
    </div>
</div>






















