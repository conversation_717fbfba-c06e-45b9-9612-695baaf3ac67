﻿using Admin.TaskDotNet.Dtos;
using AutoMapper;
using TaskDotNet.Comman.DataAccess;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TaskDotNet.Models;

namespace Admin.TaskDotNet.Controllers
{
    [Authorize(Roles = "Admin")]

    public class CompanyController : Controller
    {
        #region Ctor
        private readonly IMapper mapper;
        private readonly ApplicationDbContext _context;

        public CompanyController(IMapper mapper, ApplicationDbContext context)
        {
            this.mapper = mapper;
            _context = context;
        }
        #endregion

        #region Index
        public async Task<IActionResult> Index()
        {
            var data = await _context.Company.FirstOrDefaultAsync(m => true) ?? new();

            var model = mapper.Map<CompanyDto>(data);
            return View(model);
        }
        #endregion

        #region Edit
        public async Task<IActionResult> Edit()
        {
            var data = await _context.Company.FirstOrDefaultAsync(m => true) ?? new();
            var model = mapper.Map<CompanyDto>(data);

            return View(model);
        }
        [HttpPost]
        public async Task<IActionResult> Edit(CompanyDto dto)
        {
            if (!ModelState.IsValid)
            {
                return View(dto);
            }

            var data = await _context.Company.FirstOrDefaultAsync(m => true);

            if (data == null)
            {
                var model = mapper.Map<Company>(dto);
                _context.Company.Add(model);
            }
            else
            {
                data = mapper.Map(dto, data);
                _context.Company.Update(data);
            }

            _context.SaveChanges();

            TempData[key: "CompanyUpdated"] = "done";
            return RedirectToAction("Index");
        }
        #endregion
    }
}
