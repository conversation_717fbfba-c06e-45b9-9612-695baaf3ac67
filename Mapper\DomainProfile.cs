﻿using Admin.TaskDotNet.Dtos;
using AutoMapper;
using TaskDotNet.DTOs;
using TaskDotNet.Models;

namespace Admin.TaskDotNet.Mapper
{
    public class DomainProfile : Profile
    {
        public DomainProfile()
        {
            CreateMap<Activity, WorkersDto>();
            CreateMap<WorkersDto, Activity>();
            //---------------
            CreateMap<Activity, PaintingDto>();
            CreateMap<PaintingDto, Activity>();
            //---------------
            CreateMap<Activity, CleaningDto>();
            CreateMap<CleaningDto, Activity>();
            //---------------
            CreateMap<Activity, MovingDto>();
            CreateMap<MovingDto, Activity>();
            //---------------
            CreateMap<Activity, MovingCleaningDto>();
            CreateMap<MovingCleaningDto, Activity>();
            //---------------
            CreateMap<Activity, Archive>();
            CreateMap<Archive, Activity>();
            //---------------
            CreateMap<Activity, ActivityDto>();
            CreateMap<ActivityDto, Activity>();
            //---------------
            CreateMap<Payment, PaymentDto>();
            CreateMap<PaymentDto, Payment>();
            //---------------
            CreateMap<ActivityOrder, ActivityOrderDto>();
            CreateMap<ActivityOrderDto, ActivityOrder>();
            //---------------
            CreateMap<Inventory, InventoryDto>();
            CreateMap<InventoryDto, Inventory>();
            //---------------
            CreateMap<ActivityInventoryItem, InventoryItemDto>();
            CreateMap<InventoryItemDto, ActivityInventoryItem>();
            //---------------
            CreateMap<InventoryItem, InventoryItemHelperDto>();
            CreateMap<InventoryItemHelperDto, InventoryItem>();
            //---------------
            CreateMap<Company, CompanyDto>();
            CreateMap<CompanyDto, Company>();
            //---------------
            CreateMap<Partner, PartnerDto>();
            CreateMap<PartnerDto, Partner>();
            //---------------
            CreateMap<Archive, ArchiveDto>();
            CreateMap<ArchiveDto, Archive>();
            //---------------
            CreateMap<Movement, MovementDto>();
            CreateMap<MovementDto, Movement>();
            //---------------
            CreateMap<OTPVerification, OTPVerificationDto>();
            CreateMap<OTPVerificationDto, OTPVerification>();
            //---------------
            CreateMap<ContactUs, ContactUsDto>();
            CreateMap<ContactUsDto, ContactUs>();
            //---------------
            CreateMap<EmailText, EmailTextDto>();
            CreateMap<EmailTextDto, EmailText>();
            //---------------
        }
    }
}
