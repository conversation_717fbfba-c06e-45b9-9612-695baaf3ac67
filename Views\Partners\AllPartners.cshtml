﻿@using Comman.Helper.Extensions
@model IEnumerable<Partner>
@{
    ViewData["Title"] = "AllPartners";

    Layout = "~/Views/Shared/_Layout.cshtml";

    var confirmDeleteTitle = @SharedLocalizer["Delete record"];

    var confirmDeleteText = @SharedLocalizer["Are you sure you want to delete the selected entry?"];

    var yesDeleteText = @SharedLocalizer["Yes"];

    var noDeleteText = @SharedLocalizer["No"];
}


@section Links {
    <link src="https://cdn.datatables.net/2.0.1/css/dataTables.bootstrap5.css" />
}

<!-- Content wrapper -->
<div class="content-wrapper">
    <!-- Content -->

    <div class="container-fluid flex-grow-1 ">
        <h3 class="py-3 mb-4 fw-bold text-white">@SharedLocalizer["PartnerList"]</h3>

        <!-- Basic Bootstrap Table -->
        <div class="card">
            <div class="table-responsive text-nowrap container">
                <table id="userexample" class="table table-striped" style="width:100%">
                    <thead>
                        <tr>
                            <th>@SharedLocalizer["Company Name"] </th>
                            <th>@SharedLocalizer["Contact person"]</th>
                            <th>@SharedLocalizer["City"]</th>
                            <th>@SharedLocalizer["Email"]</th>
                            <th>@SharedLocalizer["Start Date"]</th>
                            <th>@SharedLocalizer["Status"]</th>
                            <th>@SharedLocalizer["Actions"]</th>
                        </tr>
                    </thead>
                    <tbody class="table-border-bottom-0">
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td><i class="fab fa-angular fa-lg text-danger me-3"></i> <strong>@item.CompanyName</strong></td>
                                <td>@item.PName</td>
                                <td>@item.PCity</td>
                                <td>@item.Email</td>
                                <td>@item.StartDate?.ToString("dd.MM.yyyy")</td>

                                <td>
                                    @if (item.Status == TaskDotNet.Helper.PartnerStatus.Active)
                                    {
                                        <span class="badge px-4 py-2 rounded-pill text-white" style="background-color:#1cc88a;">
                                            @SharedLocalizer["Yes"]
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="badge px-3 py-2 rounded-pill bg-danger rounded-pill text-white">
                                            @SharedLocalizer["No"]
                                        </span>
                                    }
                                </td>

                                <td>

                                    <a type="button" class="btn btn-primary" title="@SharedLocalizer["Edit"]" asp-controller="Partners" asp-action="Edit" asp-route-email="@item.Email"><i class='bx bx-edit'></i> </a>

                                    <a type="button" class="btn btn-secondary" title="@SharedLocalizer["Send"] @SharedLocalizer["Email"]" asp-controller="Partners" asp-action="SendEmail" asp-route-id="@item.Id"><i class='bx bx-envelope'></i>  </a>
                                    @if (User.IsInRole("Admin"))
                                    {
                                        <a type="button" class="btn btn-info" title="@SharedLocalizer["Orders"]" asp-controller="Partners" asp-action="PartnerOrders" asp-route-id="@item.Id">
                                            <i class='bx bx-list-ul'></i>
                                        </a>
                                        <a type="button" class="btn btn-dark" title="@SharedLocalizer["Balance"]" asp-controller="Partners" asp-action="PartnerBalanceHistory" asp-route-id="@item.Id">
                                            <i class='bx bx-money'></i>
                                        </a>

                                        <button class="btn btn-danger delete-btn" title="@SharedLocalizer["Delete"]" data-url="@Url.Action("Delete", "Partners", new { id = item.Id })">
                                            <i class='bx bx-trash-alt'></i>
                                        </button>
                                    }
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
        <!--/ Basic Bootstrap Table -->

    </div>
</div>

@functions {
    // Get badge class based on evaluation
    public string GetEvaluationBadgeClass(TaskDotNet.Helper.PartnerEvaluation evaluation)
    {
        return evaluation switch
        {
            TaskDotNet.Helper.PartnerEvaluation.New => "bg-label-info",
            TaskDotNet.Helper.PartnerEvaluation.Excellent => "bg-label-primary",
            TaskDotNet.Helper.PartnerEvaluation.Good => "bg-label-warning",
            TaskDotNet.Helper.PartnerEvaluation.Weak => "bg-label-secondary",
            _ => "bg-label-danger"
        };
    }

    // Get badge class based on status
    public string GetStatusBadgeClass(TaskDotNet.Helper.PartnerStatus status)
    {
        return status == TaskDotNet.Helper.PartnerStatus.Blocked ? "bg-label-danger" : "bg-label-primary";
    }

    // Get status text
    public string GetStatusText(TaskDotNet.Helper.PartnerStatus status)
    {
        return status == TaskDotNet.Helper.PartnerStatus.Blocked ? "Blocked" : "Not Blocked";
    }
}

@if (TempData["BlockedSuccessfully"] != null)

{
    <script>
        Swal.fire({
            title: "Done Successfully !",
            text: "this partner has been blocked successfully",
            icon: "success",
        });
    </script>
}
@if (TempData["DeletedSuccessfully"] != null)

{
    <script>
        Swal.fire({
            title: "Done Successfully !",
            text: "this partner has been deleted successfully",
            icon: "success",
        });
    </script>
}
@if (TempData["CancelBlockedSuccessfully"] != null)

{
    <script>
        Swal.fire({
            title: "Done Successfully !",
            text: "this block has been cancelled successfully",
            icon: "success",
        });
    </script>
}
@if (TempData["ErrorMessage"] != null)

{
    <script>
        Swal.fire({
            icon: "error",
            title: "Error...",
            text: "Something went wrong!"

        });
    </script>
}


@section Scripts {

    <script src="https://cdn.datatables.net/2.0.1/js/dataTables.js"></script>
    <script src="https://cdn.datatables.net/2.0.1/js/dataTables.bootstrap5.js"></script>



    <script>
        new DataTable('#userexample');

        // Additional existing scripts
        $(document).ready(function () {
            // Existing DataTable setup and filter functions
        });
    </script>
    <script>
        function confirmDelete(itemId) {
            if (confirm('Are you sure you want to delete this item?')) {
                window.location.href = '@Url.Action("Delete", "Users")/' + itemId;
            }
        }
    </script>

}




