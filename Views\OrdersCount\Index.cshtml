﻿@model IEnumerable<OrdersCount>

@{
    ViewData["Title"] = "Index";
}


<!-- Content wrapper -->
<div class="content-wrapper">
    <!-- Content -->
    <div class="container-fluid flex-grow-1 ">
        <!-- Basic Bootstrap Table -->
        <div class="card">
            <div class="container my-5">
                <div class="row align-items-center justify-content-center">
                    <h4 class="m-0 mx-5 col-md-6 fw-bold text-white text-primary text-center m-4">
                        @SharedLocalizer["OrderCountTitle"]
                    </h4>

                </div>
                <div class="row">
                    <div class="col">
                        <div class="table-responsive">
                            <table class="table table-striped table-bordered" style="width:100%">
                                <thead>
                                    <tr style="background-color:#008080">
                                        <th class="text-white">@SharedLocalizer["Activity"]</th>
                                        @foreach (var website in Model.Select(x => x.WebsiteName).Distinct())
                                        {
                                            <th class="text-white">@website</th>
                                        }
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>@SharedLocalizer["Moving"]</td>
                                        @foreach (var count in Model)
                                        {
                                            <td>@count.T_Moving</td>
                                        }
                                    </tr>
                                    <tr>
                                        <td>@SharedLocalizer["Moving & Cleaning"]</td>
                                        @foreach (var count in Model)
                                        {
                                            <td>@count.T_Mov_Clean</td>
                                        }
                                    </tr>
                                    <tr>
                                        <td>@SharedLocalizer["Cleaning"]</td>
                                        @foreach (var count in Model)
                                        {
                                            <td>@count.T_Clean</td>
                                        }
                                    </tr>
                                    <tr>
                                        <td>@SharedLocalizer["Painter & plasterer"]</td>
                                        @foreach (var count in Model)
                                        {
                                            <td>@count.T_Paint_Gips</td>
                                        }
                                    </tr>
                                    <tr>
                                        <td>@SharedLocalizer["Handyman"]</td>
                                        @foreach (var count in Model)
                                        {
                                            <td>@count.T_Workers</td>
                                        }
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="row m-5 justify-content-center">
                    <a class="btn btn-lg btn-primary px-5 text-white" style="width:160px" asp-controller="Home" asp-action="Index">@SharedLocalizer["Ok"]</a>
                    </div>
            </div>
        </div>
        <!--/ Basic Bootstrap Table -->
    </div>
</div>
