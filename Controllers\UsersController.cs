﻿using Admin.TaskDotNet.Dtos;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TaskDotNet.Comman.DataAccess;
using TaskDotNet.Models;
using TaskDotNet.Services.Auth;


namespace Admin.TaskDotNet.Controllers
{
    [Authorize(Roles = "Admin")]
    public class UsersController : Controller
    {
        #region CTOR, Fields.
        private readonly string usersForm = "UsersForm";
        private readonly ApplicationDbContext _context;
        private readonly IAuthService _authService;
        public UsersController(ApplicationDbContext context, IAuthService authService)
        {
            _context = context;
            _authService = authService;
        }
        #endregion

        #region Read Operations.
        public async Task<IActionResult> Index()
        {
            var adminUsers = await _context.AdminUsers.ToListAsync();

            var usersDto = adminUsers.Select(m => new UserDto { Id = m.Id, Email = m.Email, PName = m.Name, Role = m.Role });

            return View(usersDto);
        }

        public async Task<IActionResult> Details(string id)
        {
            var user = await _context.AdminUsers.FindAsync(id);
            if (user == null)
                return NotFound();

            var userDto = new UserDto { Email = user.Email, PName = user.Name,Role = user.Role };

            return View(userDto);
        }
        #endregion Read Operations.

        #region Create/Update Operations.
        public async Task<IActionResult> Create()
        {
            return View(nameof(usersForm), new UserDto());
        }

        public async Task<IActionResult> Update(string id)
        {
            var user = await _context.AdminUsers.FindAsync(id);
            if (user == null)
                return NotFound();

            var userDto = new UserDto { Id = user.Id,Email = user.Email, PName = user.Name,Role = user.Role };
            
            return View(nameof(usersForm), userDto);
        }

        [ValidateAntiForgeryToken]
        [HttpPost]
        public async Task<IActionResult> Save(UserDto user)
        {

            if (!ModelState.IsValid)
            {
               
                return View(nameof(usersForm), user);

            }

            if (string.IsNullOrEmpty(user.Id))
            {
                var userExist = await _authService.FindByEmailAsync(user.Email);
                if (userExist != null)
                {
                    ModelState.AddModelError("Email", "Email already exists");
                    return View(nameof(usersForm), user);
                }
                var newUser = new AdminUser
                {
                    Email = user.Email,
                    Name = user.PName,
                    UserName = user.Email,
                    Password = user.Password,
                    Role = user.Role
                };

                await _context.AdminUsers.AddAsync(newUser);
                await _context.SaveChangesAsync();

            }
            else
            {
                var userInDb = await _context.AdminUsers.Where(m=>m.Email == user.Email).FirstOrDefaultAsync();
                if (userInDb == null)
                    return NotFound();

                userInDb.Email = user.Email;
                userInDb.Name = user.PName;

                if (!string.IsNullOrWhiteSpace(user.Password))
                {
                    userInDb.Password = user.Password;
                }

                
                if (user.Role != userInDb.Role)
                {
                    userInDb.Role = user.Role;
                }

                await _context.SaveChangesAsync();
            }

            return RedirectToAction(nameof(Index));
        }
        #endregion Create/Update Operations.

        #region Delete Operation
        public async Task<IActionResult> Delete(string id)
        {
            var user = await _context.AdminUsers.FindAsync(id);
            if (user is null)
                return NotFound();

            _context.AdminUsers.Remove(user);
            await _context.SaveChangesAsync();

            return RedirectToAction("Index");

        }
        #endregion
    }
}
