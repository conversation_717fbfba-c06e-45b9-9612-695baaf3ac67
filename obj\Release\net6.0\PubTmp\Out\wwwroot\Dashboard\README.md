<p align="center">
   <a href="https://themeselection.com/products/sneat-free-bootstrap-html-admin-template/" target="_blank">
      <img src="https://user-images.githubusercontent.com/749684/150333149-805037bc-8874-4a1f-876a-61a9683f8ef5.png" alt="sneat-logo" width="30px" height="auto">
   </a>
</p>

<h1 align="center">
   <a href="https://themeselection.com/products/sneat-free-bootstrap-html-admin-template/" target="_blank" align="center">
      Sneat - Free Bootstrap 5 HTML Admin Template
   </a>
</h1>

<p align="center">Most Powerful & Comprehensive Free Bootstrap 5 HTML Admin Dashboard Template built for developers!</p>

![GitHub](https://img.shields.io/github/license/themeselection/sneat-html-admin-template-free) ![GitHub release (latest by date)](https://img.shields.io/github/v/release/themeselection/sneat-html-admin-template-free) ![GitHub issues](https://img.shields.io/github/issues/themeselection/sneat-html-admin-template-free) ![GitHub closed issues](https://img.shields.io/github/issues-closed/themeselection/sneat-html-admin-template-free) ![Twitter Follow](https://img.shields.io/twitter/follow/Theme_Selection?style=social)

[![Sneat - Bootstrap 5 HTML Admin Template Demo Screenshot](https://user-images.githubusercontent.com/80033712/153581472-452394ee-327a-4d00-a41a-c6e8036b7096.png)](https://themeselection.com/products/sneat-free-bootstrap-html-admin-template/)

## Introduction 🚀

If you’re a developer looking for most Powerful & comprehensive [**Free Bootstrap 5 HTML Admin Template**](https://themeselection.com/products/sneat-free-bootstrap-html-admin-template/) built for developers, rich with features, and highly customizable look no further than Sneat. We’ve followed the highest industry standards to bring you the very best admin template that is not only fast and easy to use but highly scalable. Offering ultimate convenience and flexibility, you’ll be able to build whatever application you want with very little hassle.

Build premium quality applications with ease. Use our innovative [bootstrap admin templates](https://themeselection.com/products/category/bootstrap-admin-templates/) to create eye-catching, high-quality WebApps. Your apps will be completely responsive, ensuring they’ll look stunning and function flawlessly on desktops, tablets, and mobile devices.

[View Demo](https://themeselection.com/demo/sneat-bootstrap-html-admin-template-free/html/)

## Installation ⚒️

Automate time-consuming or repetitive tasks in your development workflow using Gulp 🥤

> Please note, the use of Node, Webpack & Gulp and their advanced settings are totally optional. You can use and customize the template without using them too.

Installing Node & Gulp and running it is super easy in Sneat, please follow these steps and you should be ready to rock 🤘

1. First of all, make sure you have installed [Node](https://nodejs.org/en/) (LTS). If Node.js is already installed in your system, make sure the installed version is `LTS` and jump to step 2

2. Install the Gulp CLI: Open Terminal/Command Prompt and run the following command and wait until it finishes. If you have already installed Gulp CLI, you can skip this step and jump to step 3.

```bash
npm install --global gulp-cli
```

3. Navigate to the Sneat root directory and run following command to install our local dependencies listed in `package.json`. You can use `npm` OR `yarn` as per your preference.

> It is recommended to use Yarn

```bash
# For npm
npm install --legacy-peer-deps

# For Yarn
yarn
```

4. Now, you are ready to run `npm` tasks, below command will start the server and watch the code using [browsersync](https://browsersync.io/). Open [http://localhost:3000/](http://localhost:3000/) to check your development 🚀.

```bash
# npm
npm run serve

# yarn
yarn serve
```

## Available Tasks 🧑‍💻

Open console/terminal, go to the Sneat root directory and run `npm run {task_name}`.i.e To generate build run `npm run build`.

Run a task with specified environment(development/production) just execute the task with `--env={environment}` option, i.e. `npm run build --env=production`.

> **Tip:** Use `npm run` command to list all predefined npm tasks from `package.json` file.

## What's Included 📦

- Dashboard
- Layouts
  - Without menu
  - Without Navbar
  - Container
  - Fluid
  - Blank
- Pages
  - Account Settings
  - Login
  - Register
  - Forgot Password
  - Error
  - Under Maintenance
- Cards
- User Interface
  - **All Bootstrap Components**
- Extended UI
  - Perfect Scrollbar
  - Text Divider
- Boxicon
- Form Elements
  - Basic Inputs
  - Input Groups
- Form Layout
  - Vertical Form
  - Horizontal Form
- Tables

## What's in Premium Version 💎

| Sneat Free Version                                                                        | Sneat Premium Version                                                                                                            |
| ----------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------- |
| [Demo](https://themeselection.com/demo/sneat-bootstrap-html-admin-template-free/html/)    | [Demo](https://themeselection.com/demo/sneat-bootstrap-html-admin-template/html/vertical-menu-template/)                         |
| [Download](https://themeselection.com/products/sneat-free-bootstrap-html-admin-template/) | [Purchase](https://themeselection.com/products/sneat-bootstrap-html-admin-template/)                                             |
| Single vertical Menu                                                                      | Vertical Menu + Horizontal Menu                                                                                                  |
| Simple Light Style                                                                        | Light & Dark Style                                                                                                               |
| Default Theme                                                                             | Default, Semi Dark & Bordered Themes                                                                                             |
| Fixed Layout(Menu)                                                                        | Fixed & Static Layout(Menu)                                                                                                      |
| 1 Simple Dashboard                                                                        | 3 Niche Dashboards                                                                                                               |
| -                                                                                         | Multiple Ready to use Application like Calendar, Invoice, Users List, Users View, Roles and Permission etc.                      |
| Simple From Elements                                                                      | Advance form elements, validation & form wizard                                                                                  |
| Basic Cards                                                                               | Basic, Advance , Statistics, Analytics, Gamifications and Actions Cards                                                          |
| Basic User Interface(Components)                                                          | Advance and Custom User Interfaces(Components)                                                                                   |
| Two Extended Components                                                                   | Twelve Ready to use Extended Components                                                                                          |
| -                                                                                         | Quick Search - Quickly navigate between pages (w/ hotkey support)                                                                |
| Basic Pages                                                                               | Authentication Pages in 2 Variants + Ready to use pages like User Profile, Account Settings,FAQ, Help Center, Pricing, Misc etc. |
| -                                                                                         | 3D Characters + Illustrations                                                                                                    |
| Basic tables                                                                              | Advanced tables                                                                                                                  |
| -                                                                                         | Quick customization using theme config file                                                                                      |
| -                                                                                         | Leaflet Maps                                                                                                                     |
| 1 Chart Library                                                                           | 2 Chart Libraries                                                                                                                |
| -                                                                                         | Multiple Navbar & Menu Options                                                                                                   |
| -                                                                                         | Starter-kit                                                                                                                      |
| -                                                                                         | Internationalization support                                                                                                     |
| -                                                                                         | RTL Support                                                                                                                      |
| Regular Support                                                                           | Priority Support                                                                                                                 |
| Detailed Documentation                                                                    | Detailed Documentation                                                                                                           |

## Documentation 📜

Check out our live [Documentation](https://themeselection.com/demo/sneat-bootstrap-html-admin-template/documentation/)

## Browser Support 🖥️

At present, we officially aim to support the last two versions of the following browsers:

- Chrome (latest)
- FireFox (latest)
- Safari (latest)
- Microsoft Edge (latest)
- Opera (latest)

## Useful Links 🎁

- [Freebies](https://themeselection.com/products/category/download-free-admin-templates/)
- [Download Free Admin Templates](https://themeselection.com/products/category/download-free-admin-templates/)
- [Bootstrap 5 CheatSheet](https://bootstrap-cheatsheet.themeselection.com/)

## Support 👨‍💻

We use GitHub issues as support tickets to manage Item support.

1. In order to raise the GitHub issue, you must have a github account. [Raise a Issue](https://github.com/themeselection/sneat-html-admin-template-free/issues)

## License ©

- Copyright © [ThemeSelection](https://themeselection.com/)
- Licensed under [MIT](https://github.com/themeselection/sneat-html-admin-template-free/blob/master/LICENSE.md)

## Contributing 🦸

Contribution are always welcome and recommended! Here is how:

- Fork the repository ([here is the guide](https://docs.github.com/en/get-started/quickstart/fork-a-repo)).
- Clone to your machine `git clone https://github.com/YOUR_USERNAME/REPO_URL` Make your changes
- Create a pull request

### Contribution Requirements 🧰

- When you contribute, you agree to give a non-exclusive license to ThemeSelection to use that contribution in any context as we (ThemeSelection) see appropriate.
- If you use content provided by another party, it must be appropriately licensed using an open source license.
- Contributions are only accepted through Github pull requests.
- Finally, contributed code must work in all supported browsers.

## Creators 😇

- [ThemeSelection](https://themeselection.com)

## Changelog 📆

Please refer to the [CHANGELOG](https://themeselection.com/demo/sneat-bootstrap-html-admin-template-free/changelog.html) file. We will add a detailed release notes to each new release.

## Social Media 🌍

- Twitter : [https://twitter.com/Theme_Selection](https://twitter.com/Theme_Selection)
- Facebook : [https://www.facebook.com/ThemeSelections/](https://www.facebook.com/ThemeSelections/)
- Pinterest : [https://pinterest.com/themeselect/](https://pinterest.com/themeselect/)
- Instagram : [https://www.instagram.com/themeselection/](https://www.instagram.com/themeselection/)
