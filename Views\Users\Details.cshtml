﻿@using Admin.TaskDotNet.Dtos
@model UserDto
@{
    ViewBag.Title = "User Details";
}


<div class="card shadow mb-4">
    <div class="card-body">




        <div class="form-group row">
            <label asp-for="Email" class="col-sm-2 col-form-label"></label>
            <div class="col-md-6 my-auto">
                <p class="text-dark font-weight-bold my-auto">@Model.Email</p>
            </div>

        </div>


        <div class="form-group row">
            <label asp-for="PName" class="col-sm-2 col-form-label"></label>
            <div class="col-md-6 my-auto">
                <p class="text-dark font-weight-bold my-auto">@Model.PName</p>
            </div>
        </div>    

        <div class="form-group row">
            <label asp-for="Role" class="col-sm-2 col-form-label"></label>
            <div class="col-md-6 my-auto">
                <p class="text-dark font-weight-bold my-auto">@Model.Role</p>
            </div>
        </div>



        <div class="form-group row">
            <div class="col-md-12 col-sm-12">
                <a class="btn btn-success my-1" asp-area="Admin" asp-controller="Users" asp-action="Update" asp-route-id="@Model.Id" title="Update"><i class="fas fa-pencil-alt"></i>&nbsp;Update</a>
                <a class="btn btn-secondary my-1" asp-area="Admin" asp-controller="Users" asp-action="Index"><i class="fas fa-backward"></i>&nbsp;Back To List</a>
            </div>
        </div>
    </div>
</div>