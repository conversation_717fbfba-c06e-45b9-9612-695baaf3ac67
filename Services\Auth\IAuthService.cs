﻿using Admin.TaskDotNet.Helper;
using TaskDotNet.Models;

namespace TaskDotNet.Services.Auth
{
    public interface IAuthService
    {
        Task<AdminUser?> FindByEmailAsync(string email);
        Task<AdminUser?> FindByIdAsync(string id);
        Task<string> GeneratePasswordResetTokenAsync(AdminUser user);
        Task<bool> ChangePasswordAsync(AdminUser user,string oldPassword, string newPassword);
        string HashPassword(string password);
        Task<bool> LoginAsync(string email, string password, bool isPersistent);
        Task LogoutAsync();
        bool VerifyPassword(string inputPassword, string storedHash);
        bool VerifyPasswordResetToken(AdminUser user, string token);
        Task ResetPassword(AdminUser user,string newPassword);

    }
}