﻿using AutoMapper;
using TaskDotNet.Comman.DataAccess;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TaskDotNet.Services;

namespace Admin.TaskDotNet.Controllers
{
    [Authorize(Roles = "Admin, Moderator")]

    public class ContactUsController : Controller
    {
        #region Ctor
        private readonly IMapper mapper;
        private readonly ApplicationDbContext _context;

        public ContactUsController(IMapper mapper, ApplicationDbContext context)
        {
            this.mapper = mapper;
            _context = context;
        }
        #endregion

        #region Index
        public async Task<IActionResult> Index()
        {
            var model = await _context.ContactUs.ToListAsync();

            return View(model);
        }
        #endregion

        public async Task<IActionResult> Details(int id)
        {
            var contact = await _context.ContactUs.FindAsync(id);
            if (contact == null)
            {
                return NotFound();
            }
            return View(contact);
        }

        #region Delete 
        public async Task<IActionResult> Delete(int id)
        {
            var data = await _context.ContactUs.FirstOrDefaultAsync(m=>m.Id == id);
            if (data != null)
            {
                _context.ContactUs.Remove(data);
                _context.SaveChanges();
                return Json(true);

            }
            TempData[key: "ErrorMessage"] = "error";
            return Json(false);
        }
        #endregion
    }
}
