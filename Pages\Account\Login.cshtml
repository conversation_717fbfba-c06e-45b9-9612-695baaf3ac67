﻿@page
@model LoginModel

@{
    ViewData["Title"] = "Log in";
}


<style>
    .overlay-text {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        background-attachment: fixed;
    }
</style>

<section style="height:100vh">
    <div>
        <div>
            <div class="d-flex justify-content-center align-items-center flex-column vh-100" >
                <div class="row justify-content-center align-items-end mb-4">
                    <img class="col-4" src="~/Dashboard/assets/img/logo.png" style="width:250px" />
                    <h2 class="col-4 m-0" style="color:black">@SharedLocalizer["Administrator"]</h2>
                </div>
                <div class="row g-0" style="width:80%;height: 80%; background-color: white;">
                    <div class="col-md-4 d-flex align-items-center">
                        <div class="card-body text-black" style="padding:3.5rem">
                            <div class="w-100 text-center mb-5 pb-md-5">
                                <img src="~/Dashboard/assets/img/logo.png" style="width:350px" />
                            </div>
                            <form id="formAuthentication" asp-controller="Account" asp-action="Login" class="mb-3">
                                <div asp-validation-summary="All" class="text-danger"></div>
                                <div class="mb-3">
                                    <label for="Email" style="font-size:20px" class="form-label">@SharedLocalizer["Email"] </label>
                                    <input type="text" asp-for="Input.Email"
                                           class="form-control"
                                           placeholder="Enter your email " />
                                </div>
                                <div class="mb-3 form-password-toggle">
                                    <div class="d-flex justify-content-between">
                                        <label class="form-label" style="font-size:20px" for="password">@SharedLocalizer["Password"]</label>
                                    </div>
                                    <div class="input-group input-group-merge">
                                        <input asp-for="Input.Password"
                                               class="form-control"
                                               placeholder="&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;"
                                               aria-describedby="password" />
                                        <span class="input-group-text cursor-pointer"><i class="bx bx-hide"></i></span>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" asp-for="Input.RememberMe" type="checkbox" />
                                        <label class="form-check-label" asp-for="Input.RememberMe"> @SharedLocalizer["Remember Me"] </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <input class="btn btn-lg btn-primary d-grid mx-auto w-75" value="@SharedLocalizer["Login"]" type="submit" />
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="d-none d-md-block col-md-8 h-100" style="position:relative">
                        <img src="~/Dashboard/assets/img/login2.png"
                             alt="login form" class="" style="width:100%; height:100%;object-fit: fill;" />
                        <div class="overlay-text d-flex justify-content-end flex-column text-center">
                            <h4 class="text-white">@SharedLocalizer["loginDesc"]</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}