﻿using Comman.Services.Interfaces;
using Admin.TaskDotNet.Mapper;
using Admin.TaskDotNet.Services;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc.Razor;
using Microsoft.EntityFrameworkCore;
using TaskDotNet.Models;
using TaskDotNet.Services;
using TaskDotNet.Comman.DataAccess;
using Admin.TaskDotNet.Helper;
using Comman.Helper.Extensions;
using TaskDotNet.Localization;
using TaskDotNet.Services.Auth;
using Microsoft.AspNetCore.Authentication.Cookies;

namespace TaskDotNet.Helper.Extensions
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddServices(this IServiceCollection services, IConfiguration configurationParam)
        {
            services.AddDbContext<ApplicationDbContext>(options =>
                 options.UseSqlServer(configurationParam.GetConnectionString("DefultConnection"))
            );

            services.AddControllersWithViews()
                    .AddViewLocalization(LanguageViewLocationExpanderFormat.Suffix)
                    .AddDataAnnotationsLocalization(options =>
                    {
                        options.DataAnnotationLocalizerProvider  = (type, factory) =>
                            factory.Create(typeof(SharedResource));
                    });
            services.AddRazorPages();

            services.AddSession(options =>
            {
                options.IdleTimeout = TimeSpan.FromDays(1);
                options.Cookie.HttpOnly = true;
                options.Cookie.IsEssential = true;
                options.Cookie.Name = "Admin.TaskDotNet";
            });

            services.AddAutoMapper(x => x.AddProfile(new DomainProfile()));


            #region Identity

            services.AddIdentity<Partner, IdentityRole>(options =>
            {
                // Default Password settings.
                options.Password.RequireDigit = false;
                options.Password.RequireLowercase = false;
                options.Password.RequireNonAlphanumeric = false;
                options.Password.RequireUppercase = false;
                options.Password.RequiredLength = 6;
                options.Password.RequiredUniqueChars = 0;
                options.User.RequireUniqueEmail = true;

                options.Lockout.AllowedForNewUsers = true;
                options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(3);
                options.Lockout.MaxFailedAccessAttempts = 5;

                options.SignIn.RequireConfirmedEmail = true;
            })
            .AddEntityFrameworkStores<ApplicationDbContext>()
            .AddDefaultTokenProviders();

            // Configure Identity cookies properly
            services.ConfigureApplicationCookie(options =>
            {
                options.Cookie.HttpOnly = true;
                options.Cookie.SecurePolicy = CookieSecurePolicy.Always;
                options.ExpireTimeSpan = TimeSpan.FromDays(30);
                options.SlidingExpiration = true;
                options.AccessDeniedPath = "/Error/401";
                options.LoginPath = "/Account/Login";
                options.LogoutPath = "/Account/Logout";
            });

            #endregion

            #region Services.
            services.AddScoped<IMovingService, MovingService>();
            services.AddScoped<IGisperService, GisperService>();
            services.AddScoped<IPaintingService, PaintingService>();
            services.AddScoped<ICleaningService, CleaningService>();
            services.AddScoped<IActivityService, ActivityService>();
            services.AddScoped<IInventoryService, InventoryService>();
            services.AddScoped<ICompanyService, CompanyService>();
            services.AddScoped<IArchiveService, ArchiveService>();
            services.AddScoped<IMovementService, MovementService>();
            services.AddScoped<IEmailTextService, EmailTextService>();
            services.AddScoped<IMailService, MailService>();
            services.AddScoped<IFileManagerService, FileManagerService>();
            services.AddScoped<IAuthService, AuthService>();
            services.AddScoped<IEmailHtmlTemplateService, EmailHtmlTemplateService>();

            #endregion Services.

            return services;
        }

    }
}
