@model Admin.TaskDotNet.Dtos.CreditsDto
@{
    ViewData["Title"] = "Credits";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@section Links {
    <link href="https://cdn.datatables.net/2.0.1/css/dataTables.bootstrap5.css" rel="stylesheet" />
    <style>
        .date-filter {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .date-filter label {
            margin-bottom: 0;
            white-space: nowrap;
            color: white;
            font-weight: 500;
        }

        .date-filter input {
            width: 150px;
        }

        .filter-btn {
            margin-left: 10px;
        }

        .total-row {
            background-color: #f8f9fa;
            font-weight: bold;
        }

        .print-section {
            border-top: 1px solid #dee2e6;
            padding-top: 15px;
            margin-top: 15px;
        }
    </style>
}

<!-- Content wrapper -->
<div class="content-wrapper">
    <!-- Content -->
    <div class="container-fluid flex-grow-1">
        <div class="row py-2 m-0 my-2">
            <h3 class="fw-bold text-white col-md-12">
                @SharedLocalizer["Credits"]
            </h3>
        </div>

        <!-- Date Filter Form -->
        <div class="card mb-4">
            <div class="card-body">
                <form asp-action="Index" method="post">
                    <div class="date-filter">
                        <label class="text-dark" asp-for="DateFrom">@SharedLocalizer["From"]:</label>
                        <input asp-for="DateFrom" class="form-control flat-picker-date" />
                        
                        <label class="text-dark" asp-for="DateTo">@SharedLocalizer["To"]:</label>
                        <input asp-for="DateTo" class="form-control flat-picker-date" />
                        
                        <button type="submit" class="btn btn-primary filter-btn">
                            <i class="bx bx-search"></i> @SharedLocalizer["Filter"]
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Credits Table -->
        <div class="card">
            <div class="card-body">
                @if (ViewBag.Credits != null && ((List<Admin.TaskDotNet.Dtos.CreditItemDto>)ViewBag.Credits).Any())
                {
                    <!-- Print Button -->
                    <div class="d-flex justify-content-between align-items-center mb-3 print-section">
                        <h5 class="card-title mb-0">@SharedLocalizer["Credits"]</h5>
                        <a href="@Url.Action("PrintCreditsReport", new { dateFrom = Model.DateFrom.ToString("yyyy-MM-dd"), dateTo = Model.DateTo.ToString("yyyy-MM-dd") })"
                           target="_blank" class="btn btn-primary">
                            <i class="bx bx-printer me-1"></i> @SharedLocalizer["Print"]
                        </a>
                    </div>
                    <div class="table-responsive">
                        <table id="creditsTable" class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>@SharedLocalizer["Partnername"]</th>
                                    <th>@SharedLocalizer["Date"]</th>
                                    <th>@SharedLocalizer["PaymentWay"]</th>
                                    <th class="text-end">@SharedLocalizer["Amount"] (CHF)</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var credit in (List<Admin.TaskDotNet.Dtos.CreditItemDto>)ViewBag.Credits)
                                {
                                    <tr>
                                        <td>@credit.PartnerName</td>
                                        <td>@credit.PayDate.ToString("dd.MM.yyyy")</td>
                                        <td>@credit.PaymentMethod</td>
                                        <td class="text-end">@credit.Amount.ToString("N2")</td>
                                    </tr>
                                }
                            </tbody>
                            <tfoot>
                                <tr class="total-row">
                                    <td colspan="3" class="text-end"><strong>@SharedLocalizer["Total"]:</strong></td>
                                    <td class="text-end"><strong>@ViewBag.TotalAmount.ToString("N2")</strong></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                }
                else
                {
                    <div class="alert alert-info">
                        <i class="bx bx-info-circle"></i>
                        @SharedLocalizer["NoCreditsFound"]
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.datatables.net/2.0.1/js/dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/2.0.1/js/dataTables.bootstrap5.min.js"></script>

    <script>
        $(document).ready(function() {
            // Initialize DataTable
            $('#creditsTable').DataTable({
                "pageLength": 25,
                "order": [[1, "desc"]], // Order by date descending
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/@(System.Globalization.CultureInfo.CurrentCulture.Name == "de" ? "German" : System.Globalization.CultureInfo.CurrentCulture.Name == "fr" ? "French" : System.Globalization.CultureInfo.CurrentCulture.Name == "it" ? "Italian" : "English").json"
                }
            });
        });
    </script>
}
