﻿
#nullable disable

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.ComponentModel.DataAnnotations;
using System.Security.Claims;
using TaskDotNet.Comman.DataAccess;
using TaskDotNet.Models;
using TaskDotNet.Services.Auth;

namespace TaskDotNet.Pages.Account.Manage
{
    [Authorize(Roles = "Admin")]
    public class SettingsModel : PageModel
    {
        private readonly IAuthService _authService;
        private readonly ApplicationDbContext _context;
        public SettingsModel(IAuthService authService, ApplicationDbContext context)
        {

            _authService = authService;
            _context = context;
        }

        /// <summary>
        ///     This API supports the ASP.NET Core Identity default UI infrastructure and is not intended to be used
        ///     directly from your code. This API may change or be removed in future releases.
        /// </summary>
        [TempData]
        public string StatusMessage { get; set; }

        /// <summary>
        ///     This API supports the ASP.NET Core Identity default UI infrastructure and is not intended to be used
        ///     directly from your code. This API may change or be removed in future releases.
        /// </summary>
        [BindProperty]
        public InputModel Input { get; set; }

        /// <summary>
        ///     This API supports the ASP.NET Core Identity default UI infrastructure and is not intended to be used
        ///     directly from your code. This API may change or be removed in future releases.
        /// </summary>
        public class InputModel
        {
            [Required]
            [EmailAddress]
            public string Email { get; set; }
            public string Name { get; set; }
            public string PhoneNumber { get; set; }
        }


        public async Task<IActionResult> OnGetAsync()
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

            var user = await _authService.FindByIdAsync(userId);

            if (user == null)
            {
                return NotFound($"Unable to load user with ID '{userId}'.");
            }


            Input = new()
            {
                Name = user.Name,
                PhoneNumber = user.Phone,
                Email = user.Email,
            };

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {

            if (!ModelState.IsValid)
            {
                return Page();
            }

            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

            var user = await _authService.FindByIdAsync(userId);

            if (user == null)
            {
                return NotFound($"Unable to load user with ID '{userId}'.");
            }

            if (user.Email != Input.Email)
            {
                var userWithSameEmail = await _authService.FindByEmailAsync(Input.Email);
                if (userWithSameEmail != null)
                {
                    ModelState.AddModelError("Email", "Email already exists");
                    return Page();
                }
            }

            user.Name = Input.Name;
            user.Phone = Input.PhoneNumber;
            user.Email = Input.Email;

            _context.AdminUsers.Update(user);
            await _context.SaveChangesAsync();

            StatusMessage = "Your Account Settings has been changed.";

            return RedirectToPage();
        }
    }
}
