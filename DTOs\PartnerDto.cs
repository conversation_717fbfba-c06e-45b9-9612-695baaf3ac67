﻿using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
using System.ComponentModel.DataAnnotations;
using TaskDotNet.Helper;
using TaskDotNet.Models;

namespace Admin.TaskDotNet.Dtos
{
    public class PartnerDto
    {
        public string Id { get; set; }
        public Salute Salute { get; set; }
        [Required]
        public string CompanyName { get; set; }
        public string? PName { get; set; }
        [Required]
        public string PStreet { get; set; }
        [Required]
        public string PPostBox { get; set; }
        [Required]
        public string PCity { get; set; }
        [Required]
        public string Phone { get; set; }
        public string? Mobile { get; set; }
        public string? Website { get; set; }
        public string Email { get; set; }
        public decimal Saldo { get; set; }
        public string? Language { get; set; }
        [Required]
        public DateTime StartDate { get; set; } = DateTime.Now.Date;
        [Required]
        public string Land { get; set; }
        public string? IBAN { get; set; }
        public string? Bank { get; set; }
        public string? Owner { get; set; }
        [Required]
        public string UID { get; set; }
        public PartnerEvaluation Evaluation { get; set; }
        public bool Blocked { get; set; }

        [ValidateNever]
        public PartnerActivities PartnerActivities { get; set; }
        [ValidateNever]
        public PartnerCountries PartnerCountries { get; set; }
    }
}
