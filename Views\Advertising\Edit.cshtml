﻿@model Advertising
@{
    ViewData["Title"] = "Edit";
}


<div class="container-fluid flex-grow-1 ">
    <h3 class="py-3 mb-4 fw-bold text-white">@SharedLocalizer["Edit entry"]</h3>
    <div class="card shadow mb-4">
        <div class="card-body">

            <form asp-action="Edit" method="post">

                <input asp-for="Id" hidden />

                <div asp-validation-summary="All" class="text-danger"></div>
                <div class="form-group row">
                    <label asp-for="Branch" class="col-sm-2 col-form-label">@SharedLocalizer["Branch"]</label>
                    <div class="col-md-6">
                        <input asp-for="Branch" class="form-control">
                    </div>
                    <span asp-validation-for="Branch" class="text-danger"></span>
                </div>
                <div class="form-group row">
                    <label asp-for="Company" class="col-sm-2 col-form-label">@SharedLocalizer["Company"]</label>
                    <div class="col-md-6">
                        <input asp-for="Company" type="text" class="form-control">
                    </div>
                    <span asp-validation-for="Company" class="text-danger"></span>
                </div>

                <div class="form-group row">
                    <label asp-for="Street" class="col-sm-2 col-form-label">@SharedLocalizer["Street"]</label>
                    <div class="col-md-6">
                        <input asp-for="Street" class="form-control">
                    </div>
                    <span asp-validation-for="Street" class="text-danger"></span>
                </div>
                <div class="form-group row">
                    <label asp-for="BoxCity" class="col-sm-2 col-form-label">@SharedLocalizer["BoxCity"]</label>
                    <div class="col-md-6">
                        <input asp-for="BoxCity" class="form-control">
                    </div>
                    <span asp-validation-for="BoxCity" class="text-danger"></span>
                </div>

                <div class="form-group row">
                    <label asp-for="Website" class="col-sm-2 col-form-label">@SharedLocalizer["Website"]</label>
                    <div class="col-md-6">
                        <input asp-for="Website" type="text" class="form-control">
                    </div>
                    <span asp-validation-for="Website" class="text-danger"></span>
                </div>

                <div class="form-group row">
                    <label asp-for="Email" class="col-sm-2 col-form-label">@SharedLocalizer["Email"]</label>
                    <div class="col-md-6">
                        <input asp-for="Email" type="text" class="form-control">
                    </div>
                    <span asp-validation-for="Email" class="text-danger"></span>
                </div>

                <div class="form-group row">
                    <label asp-for="Telefin" class="col-sm-2 col-form-label">@SharedLocalizer["Phone"]</label>
                    <div class="col-md-6">
                        <input asp-for="Telefin" class="form-control">
                    </div>
                    <span asp-validation-for="Telefin" class="text-danger"></span>
                </div>


                <div class="form-group row">
                    <div class="col-md-12 col-sm-12">
                        <button type="submit" class=" btn btn-primary my-1 cursor-pointer"><i class="fas fa-save"></i> @SharedLocalizer["Save"]</button>
                        <a asp-action="Index" class="btn btn-secondary my-1"><i class="fas fa-window-close"></i> @SharedLocalizer["Cancel"]</a>
                    </div>
                </div>

            </form>
        </div>
    </div>
</div>


@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
