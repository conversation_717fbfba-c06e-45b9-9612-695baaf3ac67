﻿using Admin.TaskDotNet.Helper;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using System.Security.Cryptography;
using TaskDotNet.Comman.DataAccess;
using TaskDotNet.Models;

namespace TaskDotNet.Services.Auth
{
    public class AuthService : IAuthService
    {
        private readonly SignInManager<Partner> _signInManager;
        private readonly ApplicationDbContext _context;

        public AuthService(SignInManager<Partner> signInManager, ApplicationDbContext context)
        {
            _signInManager = signInManager;
            _context = context;
        }

        public async Task<bool> LoginAsync(string email, string password, bool isPersistent)
        {
            AdminUser? adminUser = await FindByEmailAsync(email);

            if (adminUser == null || !VerifyPassword(password, adminUser.Password))
                return false;

            // Create claims for the admin user
            var claims = new List<Claim>
            {
                new(ClaimTypes.Email, adminUser.Email),
                new(ClaimTypes.Name, adminUser.UserName),
                new(ClaimTypes.Role, adminUser.Role),
                new(ClaimTypes.NameIdentifier, adminUser.Id),
                new(ClaimTypes.GivenName, adminUser.Name),
            };

            var claimsIdentity = new ClaimsIdentity(claims, IdentityConstants.ApplicationScheme);
            var principal = new ClaimsPrincipal(claimsIdentity);

            var authProperties = new AuthenticationProperties
            {
                IsPersistent = isPersistent,
                ExpiresUtc = isPersistent ? DateTimeOffset.UtcNow.Add(TimeSpan.FromDays(30)) : null
            };

            await _signInManager.Context.SignInAsync(IdentityConstants.ApplicationScheme, principal, authProperties);

            return true;
        }

        public async Task LogoutAsync()
        {
            await _signInManager.SignOutAsync();
        }

        public async Task<AdminUser?> FindByEmailAsync(string email)
        {
            return await _context.AdminUsers.Where(m => m.Email == email).AsNoTracking().FirstOrDefaultAsync();
        }

        public async Task<AdminUser?> FindByIdAsync(string id)
        {
            return await _context.AdminUsers.FindAsync(id);
        }

        public string HashPassword(string password)
        {
            using var sha256 = SHA256.Create();
            var hashedBytes = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(password));
            return Convert.ToBase64String(hashedBytes);
        }

        public bool VerifyPassword(string inputPassword, string storedHash)
        {
            //return HashPassword(inputPassword) == storedHash;
            return inputPassword == storedHash;
        }

        public async Task<string> GeneratePasswordResetTokenAsync(AdminUser user)
        {
            // Generate a cryptographically secure random token
            var token = Convert.ToBase64String(RandomNumberGenerator.GetBytes(32));

            // Store the token in the database with an expiration time
            user.PasswordResetToken = token;
            user.ResetTokenExpires = DateTime.UtcNow.AddHours(1); 

            _context.AdminUsers.Update(user);
            await _context.SaveChangesAsync();

            return token;
        }
        
        public async Task ResetPassword(AdminUser user, string newPassword)
        {
            user.Password = newPassword;
            user.PasswordResetToken = null;
            user.ResetTokenExpires = null;

            _context.AdminUsers.Update(user);
            await _context.SaveChangesAsync();
        }

        public bool VerifyPasswordResetToken(AdminUser user, string token)
        {
            // Check if the token matches and has not expired
            return user.PasswordResetToken == token && user.ResetTokenExpires > DateTime.UtcNow;
        }

        public async Task<bool> ChangePasswordAsync(AdminUser user, string oldPassword, string newPassword)
        {
            var userIndatabase = await FindByIdAsync(user.Id);
            if (userIndatabase == null)
            {
                return false;
            }

            if (userIndatabase.Password != oldPassword)
            {
                return false; 
            }

            userIndatabase.Password = newPassword;
            _context.AdminUsers.Update(user);
            await _context.SaveChangesAsync();

            return true; 
        }

    }
}