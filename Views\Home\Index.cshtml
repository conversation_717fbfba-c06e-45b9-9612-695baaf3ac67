﻿@model Admin.TaskDotNet.Dtos.HomeDto
@{
    ViewData["Title"] = "Home Page";
}

<div class="content-wrapper">
    <!-- Content -->

    <div class="container-fluid flex-grow-1 ">
        <div class="row">
            <div class="col-lg-12 mb-4 order-0">
                <div class="card" style="">
                    <div class="d-flex align-items-center row">
                        <div class="col-sm-6">
                            <div class="card-body">
                                <h2 class="card-title text-primary" style="font-weight: 800;">
                                    @Html.Raw(SharedLocalizer["AdminDashboardHeader"])
                                </h2>

                            </div>
                        </div>
                        <div class="col-sm-6 text-center text-sm-end">
                            <div class="card-body pb-0 px-0 pt-0">
                                <img src="~/Dashboard/assets/img/Dashboard1.png"
                                     height="140" class="w-100"
                                     alt="View Badge User"
                                     data-app-dark-img="illustrations/man-with-laptop-dark.png"
                                     data-app-light-img="illustrations/man-with-laptop-light.png" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <div class="row">
            <div class="col-lg-2 col-md-12 col-6 mb-4">
                <div class="card">
                    <div class="card-body">
                        <div class="card-title d-flex align-items-start justify-content-between">
                            <div class="avatar flex-shrink-0">
                                <img src="~/Dashboard/assets/img/icons/unicons/chart-success.png"
                                     alt="chart success"
                                     class="rounded" />
                            </div>

                        </div>
                        <span class="fw-semibold d-block mb-1 fs-5">@SharedLocalizer["All Activity"]</span>
                        <h3 class="card-title mb-2">@Model.AllActivitiesCount</h3>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-12 col-6 mb-4">
                <div class="card">
                    <div class="card-body">
                        <div class="card-title d-flex align-items-start justify-content-between">
                            <div class="avatar flex-shrink-0">
                                <img src="~/Dashboard/assets/img/icons/unicons/chart-success.png"
                                     alt="chart success"
                                     class="rounded" />
                            </div>

                        </div>
                        <span class="fw-semibold d-block mb-1 fs-5">@SharedLocalizer["Moving"]</span>
                        <h3 class="card-title mb-2">@Model.MovingCount</h3>

                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-12 col-6 mb-4">
                <div class="card">
                    <div class="card-body">
                        <div class="card-title d-flex align-items-start justify-content-between">
                            <div class="avatar flex-shrink-0">
                                <img src="~/Dashboard/assets/img/icons/unicons/chart-success.png"
                                     alt="chart success"
                                     class="rounded" />
                            </div>

                        </div>
                        <span class="fw-semibold d-block mb-1 fs-5">@SharedLocalizer["Cleaning"]</span>
                        <h3 class="card-title mb-2">@Model.CleaningCount</h3>

                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-12 col-6 mb-4">
                <div class="card">
                    <div class="card-body">
                        <div class="card-title d-flex align-items-start justify-content-between">
                            <div class="avatar flex-shrink-0">
                                <img src="~/Dashboard/assets/img/icons/unicons/chart-success.png"
                                     alt="chart success"
                                     class="rounded" />
                            </div>

                        </div>
                        <span class="fw-semibold d-block mb-1 fs-5 text-nowrap">@SharedLocalizer["MovingAndCleaning"]</span>
                        <h3 class="card-title mb-2">@Model.MovingAndCleaningCount</h3>

                    </div>
                </div>
            </div>

            <div class="col-lg-2 col-md-12 col-6 mb-4">
                <div class="card">
                    <div class="card-body">
                        <div class="card-title d-flex align-items-start justify-content-between">
                            <div class="avatar flex-shrink-0">
                                <img src="~/Dashboard/assets/img/icons/unicons/chart-success.png"
                                     alt="chart success"
                                     class="rounded" />
                            </div>

                        </div>
                        <span class="fw-semibold d-block mb-1 fs-5 text-nowrap">@SharedLocalizer["PaintingAndGisper"]</span>
                        <h3 class="card-title mb-2">@Model.PaintingAndGisperCount</h3>

                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-12 col-6 mb-4">
                <div class="card">
                    <div class="card-body">
                        <div class="card-title d-flex align-items-start justify-content-between">
                            <div class="avatar flex-shrink-0">
                                <img src="~/Dashboard/assets/img/icons/unicons/chart-success.png"
                                     alt="chart success"
                                     class="rounded" />
                            </div>

                        </div>
                        <span class="fw-semibold d-block mb-1 fs-5">@SharedLocalizer["Handyman"]</span>
                        <h3 class="card-title mb-2">@Model.WorkersCount</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="align-content-center d-flex h-50 row " style="background-image:url('/Dashboard/assets/img/Dashboard2.png');background-position: left;background-size: cover;">
            <div class="col-md-6 px-5">
                <p style="font-size: 30px;font-weight: 800;color: white;" class="">
                      @SharedLocalizer["AdminDashboardHeader2"]
                </p>
            </div>
        </div>
    </div>
</div>
