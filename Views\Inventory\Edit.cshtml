﻿
@model Admin.TaskDotNet.Dtos.InventoryDto
@{
    ViewData["Title"] = "Edit";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="content-wrapper">
    <!-- Content -->

    <div class="container-fluid flex-grow-1 ">
        <h3 class="fw-bold text-white py-3 mb-4"><span ></span>@SharedLocalizer["Edit category (rooms)"] </h3>

        <div class="row">
            <div class="col-md-12">

                <div class="card mb-4">
                    <!-- Account -->

                    <hr class="my-0" />
                    <div class="card-body">
                        <form asp-controller="Inventory" asp-action="Edit" enctype="multipart/form-data">
                            <input type="hidden" asp-for="Id" />
                            <div class="row">
                                <div class="mb-3 col-md-12">
                                    <label for="Name" class="form-label">Name DE</label>
                                    <input class="form-control"
                                           type="text"
                                           asp-for="Categ_de" />
                                </div>
                                <div class="mb-3 col-md-12">
                                    <label for="Name" class="form-label">Name EN</label>
                                    <input class="form-control"
                                           type="text"
                                           asp-for="CategoryName" />
                                </div>
                              
                                <div class="mb-3 col-md-12">
                                    <label for="Name" class="form-label">Name FR</label>
                                    <input class="form-control"
                                           type="text"
                                           asp-for="Categ_fr" />
                                </div>
                                <div class="mb-3 col-md-12">
                                    <label for="Name" class="form-label">Name IT</label>
                                    <input class="form-control"
                                           type="text"
                                           asp-for="Categ_It" />
                                </div>

                            </div>
                            <div class="mt-2">
                                <input type="submit" class="btn btn-primary me-2" value="@SharedLocalizer["Save"]" />
                                <a asp-controller="Inventory" asp-action="Index" class="btn btn-outline-secondary">@SharedLocalizer["Cancel"]  </a>
                            </div>
                        </form>
                    </div>
                    <!-- /Account -->
                </div>

            </div>
        </div>
    </div>
    <!-- / Content -->
</div>
@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>



    @if (TempData["ErrorMessage"] != null)
    {
        <script>
            Swal.fire({
                icon: "error",
                title: "Error...",
                text: "Something went wrong!"

            });
        </script>
    }

}
