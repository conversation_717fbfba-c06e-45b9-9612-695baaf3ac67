﻿using Admin.TaskDotNet.Dtos;
using AutoMapper;
using TaskDotNet.Comman.DataAccess;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TaskDotNet.Models;

namespace Admin.TaskDotNet.Controllers
{
    [Authorize(Roles = "Admin")]
    public class ResetDataController : Controller
    {
        #region Ctor
        private readonly ApplicationDbContext _context;

        public ResetDataController(ApplicationDbContext context)
        {
            _context = context;
        }
        #endregion

        #region Index
        public IActionResult Reset()
        {
            return View(new ResetOrdersCountDto { DateFrom = DateTime.UtcNow.Date, DateTo = DateTime.UtcNow.Date });
        }

        [HttpPost]
        public async Task<IActionResult> Reset(ResetOrdersCountDto dto)
        {
            var movements = await _context.Movements.Where(m => m.Purchase_date >= dto.DateFrom && m.Purchase_date <= dto.DateTo)
                .Include(m=>m.Activity).ThenInclude(m=>m.InventoryItems)
                .ToListAsync();

            foreach (var movement in movements)
            {
                if (movement.Activity != null)
                {
                    // Remove InventoryItems related to the Activity
                    _context.RemoveRange(movement.Activity.InventoryItems);

                    // Remove the Activity itself
                    _context.Remove(movement.Activity);
                }
            }

            _context.RemoveRange(movements);

            if (dto.ResetOrdersCount)
            {
                var orderCount = await _context.OrdersCount.Where(m => m.WebsiteName == "TaskDotNet").FirstOrDefaultAsync();
                if (orderCount != null)
                {

                    orderCount.T_Workers = 0;
                    orderCount.T_Mov_Clean = 0;
                    orderCount.T_Clean = 0;
                    orderCount.T_Paint_Gips = 0;
                    orderCount.T_Moving = 0;

                }
            }
            await _context.SaveChangesAsync();

            return RedirectToAction("Index", "Home");
        }
        #endregion

    }
}
