﻿using Admin.TaskDotNet.Dtos;
using AutoMapper;
using TaskDotNet.Comman.DataAccess;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TaskDotNet.Models;

namespace Admin.TaskDotNet.Controllers
{
    [Authorize(Roles = "Admin, Moderator")]
    public class OrdersCountController : Controller
    {
        #region Ctor
        private readonly ApplicationDbContext _context;

        public OrdersCountController(ApplicationDbContext context)
        {
            _context = context;
        }
        #endregion

        #region Index
        public async Task<IActionResult> Index()
        {
            var model = await _context.OrdersCount.ToListAsync();

            return View(model);
        }
        #endregion

    

    }
}
