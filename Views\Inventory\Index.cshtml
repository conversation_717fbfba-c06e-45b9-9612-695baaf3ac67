﻿@model IEnumerable<Admin.TaskDotNet.Dtos.InventoryDto>
@{
    ViewData["Title"] = "Index";



    Layout = "~/Views/Shared/_Layout.cshtml";



    var confirmDeleteTitle = @SharedLocalizer["Delete record"];



    var confirmDeleteText = @SharedLocalizer["Are you sure you want to delete the selected entry?"];



    var yesDeleteText = @SharedLocalizer["Yes"];



    var noDeleteText = @SharedLocalizer["No"];
}
<script>
    // Pass the translated text to JavaScript variables

    var confirmDeleteTitle = '@confirmDeleteTitle';
    var confirmDeleteText = '@Html.Raw(confirmDeleteText)';
    var yesDeleteText = '@yesDeleteText';
    var noDeleteText = '@noDeleteText';
</script>
@section Links {
    <link src="https://cdn.datatables.net/2.0.1/css/dataTables.bootstrap5.css" />
    <!-- <PERSON>trap CSS -->
    <!-- jQuery (necessary for Bootstrap's JavaScript plugins) -->
    <!-- Bootstrap JS -->
}

<!-- Content wrapper -->
<div class="content-wrapper">
    <!-- Content -->

    <div class="container-fluid flex-grow-1 ">
        <h3 class="fw-bold text-white py-3 mb-4">@SharedLocalizer["RoomList"]</h3>

        <!-- Basic Bootstrap Table -->
        <div class="card">
            @if (User.IsInRole("Admin"))
            {
                <a class="btn btn-primary m-4" style="width:200px" asp-controller="Inventory" asp-action="Create">@SharedLocalizer["Create a room"]</a>
            }
            <div class="table-responsive text-nowrap container">
                <table id="example" class="table table-striped" style="width:100%">
                    <thead>
                        <tr>
                            <th>Name DE</th>
                            <th>EN</th>
                            <th>FR</th>
                            <th>IT</th>
                            @if (User.IsInRole("Admin"))

                            {
                                <th>@SharedLocalizer["Edit"]</th>
                                <th>@SharedLocalizer["Delete"]</th>
                            }
                        </tr>
                    </thead>
                    <tbody class="table-border-bottom-0">
                        @foreach (var item in Model)

                        {
                            <tr>
                                <td><i class="fab fa-angular fa-lg text-danger me-3"></i> <strong>@item.Categ_de</strong></td>
                                <td>@item.CategoryName</td>
                                <td>@item.Categ_fr</td>
                                <td>@item.Categ_It</td>
                                @if (User.IsInRole("Admin"))

                                {
                                    <td>
                                        <a class="btn btn-primary" asp-controller="Inventory" asp-action="Edit" asp-route-id="@item.Id"><i class="bx bx-edit-alt me-1"></i>@SharedLocalizer["Edit"] </a>
                                    </td>
                                    <td>
                                        <button class="btn btn-danger delete-btn" data-url="@Url.Action("Delete", "Inventory", new { id = item.Id })">
                                            @SharedLocalizer["Delete"]
                                        </button>
                                    </td>
                                }
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
        <!--/ Basic Bootstrap Table -->

    </div>
</div>
@section Scripts {


    @if (TempData["InventoryCreated"] != null)



    {
        <script>
            Swal.fire({
                title: "Created Successfully !",
                text: "Inventory has been created successfully",
                icon: "success",
            });
        </script>

    }
    @if (TempData["InventoryDeleted"] != null)



    {
        <script>
            Swal.fire({
                title: "Deleted Successfully !",
                text: "Inventory has been deleted successfully",
                icon: "success",
            });
        </script>

    }

    @if (TempData["InventoryUpdated"] != null)



    {
        <script>
            Swal.fire({
                title: "Updated Successfully !",
                text: "Inventory has been updated successfully",
                icon: "success",
            });
        </script>

    }

    @if (TempData["ErrorMessage"] != null)



    {
        <script>
            Swal.fire({
                icon: "error",
                title: "Error...",
                text: "Something went wrong!"

            });
        </script>
    }



    <script src="https://cdn.datatables.net/2.0.1/js/dataTables.js"></script>
    <script src="https://cdn.datatables.net/2.0.1/js/dataTables.bootstrap5.js"></script>



    <script>
        new DataTable('#example');

        // Additional existing scripts
        $(document).ready(function () {
            // Existing DataTable setup and filter functions
        });
    </script>

}



