﻿@* @model Admin.TaskDotNet.Dtos.StatisticsDto *@
@model Dictionary<string, Dictionary<string, int>>

@{
    ViewData["Title"] = "AdminStatistics";

    var currentMonthandYear = DateTime.UtcNow.ToString("yyyy");
    var eachActivityCountAndSum = ViewBag.EachActivityCountAndSum as Dictionary<string, (int Count, decimal Sum)>;
}

@section Links {
    <style>
        .dt-search {
            width: 250px;
        }
    </style>
}

<!-- Content wrapper -->
<div class="content-wrapper">
    <!-- Content -->
    <div class="container-fluid flex-grow-1 ">
        <h4 class="fw-boldtext-body ">
            @SharedLocalizer["General statistics"]
        </h4>

        <!-- Basic Bootstrap Table -->
        <div class="card">
            <div class="container my-5">
                <div class="row align-items-center justify-content-center">

                    @* <button class="btn btn-primary m-4 col-md-5" id="createButton" style="width:200px">@SharedLocalizer["Statistic List"]</button> *@
                    <h4 class="m-0 mx-5 col-md-6 fw-bold text-white text-primary text-center m-4">
                        @SharedLocalizer["StatisticsPageTitle"]
                    </h4>

                </div>
                <div class="row">
                    <div class="col">
                        <div class="table-responsive">
                            <table class="table table-striped table-bordered" style="width:100%">
                                <thead class="bg-label-primary">
                                    <tr style="background-color:#008080">
                                        <th class="text-white">Month</th> <!-- Placeholder for empty cell in top-left corner -->
                                        @foreach (var category in Model.Keys)

                                        {
                                            <th class="text-center text-white " style=" text-wrap: nowrap; ">@category</th>
                                        }
                                    </tr>
                                </thead>
                                <tbody class="table-border-bottom-0">
                                    @foreach (var category in Model.FirstOrDefault().Value?.Keys)
                                    {
                                        <tr>
                                            <td >@category</td>
                                            @foreach (var month in Model.Keys)
                                            {
                                                <td class="text-center">@Model[month][category]</td>
                                            }
                                        </tr>
                                    }

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <hr class="bg-primary my-5" />
                <div>
                    <h4 class="fw-bold mb-4 text-primary text-center">
                        @SharedLocalizer["The achievements of partnere for the current month"] : @currentMonthandYear
                    </h4>
                </div>
                <div class="row">
                    <div class="col">
                        <div class="table-responsive">
                            <table class="table table-striped table-bordered" style="width:100%">
                                <thead class="bg-label-primary">
                                    <tr style="background-color:#008080">
                                        <th></th>
                                        @foreach (var item in eachActivityCountAndSum)
                                        {
                                            <th class="text-center text-white" style=" text-wrap: nowrap; ">@item.Key</th>
                                        }
                                    </tr>
                                </thead>
                                <tbody class="table-border-bottom-0">
                                    <tr>
                                        <td>@SharedLocalizer["Purchased orders"]</td>
                                        @foreach (var item in eachActivityCountAndSum)
                                        {
                                            <td class="text-center">@item.Value.Count</td>
                                        }
                                    </tr>
                                    <tr>
                                        <td>@SharedLocalizer["Paid costs CHF"]</td>
                                        @foreach (var item in eachActivityCountAndSum)
                                        {
                                            <td class="text-center">@item.Value.Sum</td>
                                        }
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--/ Basic Bootstrap Table -->
    </div>
</div>

<div id="statisticsModal" class="modal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header" style="display: flex;align-items: center;justify-content: center;">
                <h5 class="modal-title">Allgemeine Statistiken</h5>
            </div>
            <div class="modal-body">
                <form id="statisticsForm" method="post" action="/Statistics/GeneratePdf">
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="AllPartners" id="allPartners" value="true" checked>
                        <label class="form-check-label" for="allPartners">
                            Alle Partner
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="AllPartners" id="specificPartner" value="false">
                        <label class="form-check-label" for="specificPartner">
                            Nur für Partner:
                        </label>
                        <input type="text" class="form-control" name="CompanyName" id="companyName" placeholder="Company name" disabled>
                    </div>
                    <div class="form-group">
                        <label for="periodFrom">Periode von:</label>
                        <input type="date" class="form-control" name="PeriodFrom" id="periodFrom" required>
                    </div>
                    <div class="form-group">
                        <label for="periodTo">bis:</label>
                        <input type="date" class="form-control" name="PeriodTo" id="periodTo" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" id="submitFormButton" class="btn btn-primary">Als PDF drucken</button>
                <button type="button" class="btn btn-secondary" data-dismiss="modal" id="cancelButton">Abbrechen</button>
            </div>
        </div>
    </div>
</div>

<!-- jQuery and Bootstrap JS -->
<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.7/umd/popper.min.js"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js"></script>

<!-- Custom JavaScript -->
<script>
    $(document).ready(function () {
        $('#createButton').on('click', function () {
            $('#statisticsModal').modal('show');
        });

        $('#specificPartner').on('change', function () {
            $('#companyName').prop('disabled', false);
        });

        $('#allPartners').on('change', function () {
            $('#companyName').prop('disabled', true);
        });

        $('#submitFormButton').on('click', function () {
            $('#statisticsForm').submit();
        });

        $('#cancelButton').on('click', function () {
            $('#statisticsModal').modal('hide');
        });
    });
</script>

@section Scripts {
}
