/* _content/Admin.TaskDotNet/Views/Shared/_Layout.cshtml.rz.scp.css */
/* Please see documentation at https://docs.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-aecdfm8m25] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-aecdfm8m25] {
  color: #0077cc;
}

.btn-primary[b-aecdfm8m25] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-aecdfm8m25], .nav-pills .show > .nav-link[b-aecdfm8m25] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-aecdfm8m25] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-aecdfm8m25] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-aecdfm8m25] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-aecdfm8m25] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-aecdfm8m25] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
