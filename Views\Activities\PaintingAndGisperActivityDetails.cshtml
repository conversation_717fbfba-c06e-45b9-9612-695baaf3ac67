﻿@model Admin.TaskDotNet.Dtos.ActivityDto
@{
    ViewData["Title"] = "ActivityDetails";
}

<div class="content-wrapper">
    <div class="container-fluid flex-grow-1">
        <h3 class="fw-bold text-white py-3 mb-4"><a title="back" class="text-white" asp-action="GetAllActivities"><i class='bx bxs-left-arrow-circle' style="font-size:2.1rem"></i></a>  <span>@SharedLocalizer["Activity"] /</span> @SharedLocalizer["ActivityDetails"]</h3>

        <div class="row">
            <div class="col-md-12">
                <div class="card mb-4">
                    
                    <div class="card-body">
                        <div id="ActivityContent" style="max-width: 700px; margin: auto; font-size: 18px;">
                            <!-- header  -->
                            <h3 style="color:#008284;font-family: arial; margin: 0; text-align: center; border: 1px solid #008284">
                                @SharedLocalizer["NewRequest"]: #@Model.OrderNr
                            </h3>
                            <div style="padding-top: 10px">
                                <p style="padding-left: 20px">@SharedLocalizer["Hello"],</p>
                                <p style="padding-left: 20px">
                                    @SharedLocalizer["ThisIsRequiredRequest"]
                                </p>

                                <div style="background-color: #008284; height: 3px"></div>

                                <table style="padding: 5px 0; width: 100%">
                                    <tr>
                                        <td style="width: 25%"></td>
                                        <td style="width: 35%; font-weight: bold">@SharedLocalizer["Category"]:</td>
                                        <td style="width: 35%; color: black;font-weight: bold;">@SharedLocalizer["Painting & Gipser"]</td>
                                    </tr>
                                    <tr>
                                        <td></td>
                                        <td style="font-weight: bold">@SharedLocalizer["ExecutionDate"]:</td>
                                        <td>@Model.MovingDate.ToString("dd.MM.yyyy")</td>
                                    </tr>
                                </table>

                                <table style="padding: 5px 0; width: 100%">
                                    <tr>
                                        <td style="width: 26.5%"></td>
                                        <td style="font-weight: bold; vertical-align: top">@SharedLocalizer["Customer"]:</td>
                                        <td style="vertical-align: top">
                                            <p style="margin: 0;color: black;font-weight: bold;">
                                                @Model.Name<br />
                                                @Model.Street, <br />
                                                @Model.PostBox @Model.City <br />
                                                @Model.Phone<br />
                                                @Model.Email
                                            </p>
                                        </td>
                                    </tr>
                                </table>

                                <div style="background-color: #008284; height: 3px"></div>

                                <table style="margin: 20px 0; width: 100%;">
                                    <tbody>
                                        <tr>
                                            <td style="width: 26.5%"></td>
                                            <td style="font-weight: bold;">@SharedLocalizer["Object"]:</td>
                                            <td style="vertical-align: top;color: black;font-weight: bold;">
                                                @Model.Room @SharedLocalizer["Room"]-@SharedLocalizer[Model.Object]<br />
                                                @SharedLocalizer[Model.Floor]
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width: 26.5%"></td>
                                            <td style="font-weight: bold;">@SharedLocalizer["Area"]:</td>
                                            <td style="color: black;font-weight: bold;">
                                                @Model.Area m²
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width: 26.5%"></td>
                                            <td style="font-weight: bold;">@SharedLocalizer["Workspace"]:</td>
                                            <td style="color: black;font-weight: bold;">
                                                @SharedLocalizer[Model.Workspace]
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>

                                <div style="background-color: #008284; height: 4px; width: 60%; margin: 5px auto 0;"></div>

                                <table style="width: 100%; margin: 20px 0">
                                    <tbody>
                                        <tr>
                                            <td style="font-weight: bold;padding-bottom: 25px;" colspan="5">@SharedLocalizer["AdditionalServices"]:</td>
                                        </tr>
                                        <tr>
                                            <td style="width: 26.5%"></td>
                                            <td style="font-weight: bold">@SharedLocalizer["Washroom"]:</td>
                                            <td style="padding-left: 10px">@((Model.Washroom) ?? false ? SharedLocalizer["Yes"] : SharedLocalizer["No"])</td>
                                            <td rowspan="4" style="border-left: 3px solid #008284; padding-left: 10px; font-weight: bold;">
                                                @SharedLocalizer["Walls"]<br />
                                                @SharedLocalizer["Doors"]<br />
                                                @SharedLocalizer["Windows"]
                                            </td>
                                            <td rowspan="4" style="padding-left: 10px">
                                                @Model.Walls<br />
                                                @Model.Doors<br />
                                                @Model.Windows
                                            </td>
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td style="font-weight: bold">@SharedLocalizer["Flexibility"]:</td>
                                            <td style="padding-left: 10px">@SharedLocalizer[Model.Flexible]</td>
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td style="font-weight: bold">@SharedLocalizer["CustomerFocus"]:</td>
                                            <td style="padding-left: 10px">@SharedLocalizer[Model.Focus]</td>
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td style="font-weight: bold">@SharedLocalizer["Viewing"]:</td>
                                            <td style="padding-left: 10px">
                                                @((Model.ViewingDate != DateTime.MinValue) ? $"{SharedLocalizer["Yes"]}, {Model.ViewingDate:dd.MM.yyyy}" : SharedLocalizer["NotRequired"])
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <!-- separator  -->
                                <div style="background-color: #008284; height: 4px; width: 60%; margin: 5px auto 0;"></div>

                                <div style="padding-top: 10px">
                                    <p><b>@SharedLocalizer["Notes"]:</b> @Model.Notes</p>
                                </div>

                                <div style="background-color: #008284; height: 4px; width: 60%; margin: 5px auto 0;"></div>

                                <p style="padding-top: 10px;font-weight: 800">@SharedLocalizer["Team"]</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
