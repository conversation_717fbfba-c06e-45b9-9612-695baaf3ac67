﻿using Admin.TaskDotNet.Dtos;
using Comman.Services.Interfaces;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TaskDotNet.Models;
using Admin.TaskDotNet.Services;
using TaskDotNet.Comman.DataAccess;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc.Rendering;
using TaskDotNet.Helper;
using ClosedXML.Excel;
using DocumentFormat.OpenXml.InkML;
using Microsoft.Extensions.Localization;
using TaskDotNet.Localization;


namespace Admin.TaskDotNet.Controllers
{
    [Authorize(Roles = "Admin,Moderator")]

    public class AdvertisingController : Controller
    {

        #region Ctor

        private readonly IMapper mapper;
        private readonly IEmailTextService emailTextService;
        private readonly IMailService mailService;
        private readonly ApplicationDbContext context;
        private readonly IStringLocalizer<SharedResource> SharedLocalizer;
        public AdvertisingController(IMapper mapper, ApplicationDbContext context, IEmailTextService emailTextService, IMailService mailService, IStringLocalizer<SharedResource> sharedLocalizer)
        {
            this.mapper = mapper;
            this.context = context;
            this.emailTextService = emailTextService;
            this.mailService = mailService;
            SharedLocalizer = sharedLocalizer;
        }
        #endregion

        #region Index
        public async Task<IActionResult> Index()
        {
            var data = await context.Advertising.ToListAsync();

            return View(data);
        }
        #endregion      

        #region Create
        [HttpGet]
        public IActionResult Create()
        {
            return View(new Advertising());
        }
        [HttpPost]
        public async Task<IActionResult> Create(Advertising model)
        {
            if (!ModelState.IsValid)
            {
                return View();
            }
            await context.Advertising.AddAsync(model);
            await context.SaveChangesAsync();

            return RedirectToAction("Index");
        }
        #endregion

        #region Edit
        [HttpGet]
        public async Task<IActionResult> Edit(int id)
        {
            var model = await context.Advertising.FindAsync(id);
            if (model == null)
            {
                return NotFound();
            }
            return View(model);
        }

        [HttpPost]
        public async Task<IActionResult> Edit(Advertising model)
        {
            if (!ModelState.IsValid)
            {
                return View();
            }
            context.Advertising.Update(model);

            await context.SaveChangesAsync();

            return RedirectToAction("Index");


        }
        #endregion

        #region Delete


        public async Task<IActionResult> Delete(int id)
        {
            var model = await context.Advertising.FindAsync(id);
            if (model == null)
            {
                return NotFound();
            }

            context.Advertising.Remove(model);
            await context.SaveChangesAsync();

            return RedirectToAction("Index");

        }
        #endregion

        #region SendEmail
        public async Task<IActionResult> SendEmail()
        {
            await PopulateViewBags();

            return View();
        }

        [HttpPost]
        public async Task<IActionResult> SendEmail(AdvertisingEmailDto dto)
        {
            if (!ModelState.IsValid)
            {
                await PopulateViewBags();
                return View(dto);
            }

            // Get advertising records with only required fields
            var advertisingQuery = dto.Branch.Equals("all", StringComparison.OrdinalIgnoreCase)
                ? context.Advertising.AsQueryable()
                : context.Advertising.Where(m => m.Branch == dto.Branch);

            var advertisingList = await advertisingQuery
                .Select(a => new { a.Id, a.Email })
                .ToListAsync();

            if (!advertisingList.Any())
            {
                TempData["error"] = "No advertising records found for the selected branch.";
                return RedirectToAction("Index");
            }

            // Create mail requests
            var mailRequests = advertisingList.Select(item => new MailRequest
            {
                ToEmail = item.Email,
                Subject = dto.Title,
                Body = dto.Message
            }).ToList();

            // Send emails concurrently and track results
            var emailTasks = mailRequests.Select(async (mailRequest, index) =>
            {
                try
                {
                    await mailService.SendEmailAsync(mailRequest, default);
                    return new EmailResult
                    {
                        Id = advertisingList[index].Id,
                        Email = advertisingList[index].Email,
                        Success = true,
                        ErrorMessage = null
                    };
                }
                catch (Exception ex)
                {
                    return new EmailResult
                    {
                        Id = advertisingList[index].Id,
                        Email = advertisingList[index].Email,
                        Success = false,
                        ErrorMessage = ex.Message
                    };
                }
            });

            var emailResults = await Task.WhenAll(emailTasks);

            // Update database efficiently with individual statuses
            var now = DateTime.Now;
            var updateStatements = emailResults.Select(result =>
                new {
                    Id = result.Id,
                    Status = result.Success ? "Success" : "Failed",
                    Versand = (DateTime?)now,
                    ErrorMessage = result.ErrorMessage
                });

            // Use a more efficient update approach
            await UpdateEmailStatusBatchRawSql(updateStatements, now);

            // Provide feedback to user
            var successCount = emailResults.Count(r => r.Success);
            var failureCount = emailResults.Length - successCount;

            if (failureCount > 0)
            {
                TempData["success"] = $"Emails sent: {successCount} successful, {failureCount} failed. Check individual statuses for details.";
            }
            else
            {
                TempData["success"] = $"All {successCount} emails sent successfully!";
            }

            return RedirectToAction("Index");
        }
        #endregion

        [HttpPost]
        public IActionResult UploadFile(IFormFile file)
        {
            if (file == null || file.Length == 0)
            {
                TempData["error"] = "No file uploaded.";
                return RedirectToAction("Index");
            }

            try
            {
                using (var stream = new MemoryStream())
                {
                    file.CopyTo(stream);
                    stream.Position = 0;

                    // Load the Excel file
                    var workbook = new XLWorkbook(stream);
                    var worksheet = workbook.Worksheet(1);

                    // Read rows from the Excel sheet
                    var advertisings = new List<Advertising>();
                    var rows = worksheet.RowsUsed();

                    foreach (var row in rows)
                    {
                        advertisings.Add(new Advertising
                        {
                            Branch = row.Cell(1).GetString(),
                            Company = row.Cell(2).GetString(),
                            Street = row.Cell(3).GetString(),
                            BoxCity = row.Cell(4).GetString(),
                            Website = row.Cell(5).GetString(),
                            Email = row.Cell(6).GetString(),
                            Telefin = row.Cell(7).GetString(),
                        });
                    }

                    // Insert data into the database
                    context.Advertising.AddRange(advertisings);
                    context.SaveChanges();

                    TempData["success"] = SharedLocalizer["Data imported successfully."].Value;
                }
            }
            catch (Exception ex)
            {
                TempData["error"] = $"Error importing data: {ex.Message}";
            }

            return RedirectToAction("Index");
        }

        [HttpGet]
        public IActionResult ExportToExcel()
        {
            var advertisings = context.Advertising.AsNoTracking().ToList();
            using (var workbook = new XLWorkbook())
            {
                var worksheet = workbook.Worksheets.Add("Advertising Data");
                // Add headers
                worksheet.Cell(1, 1).Value = "Branch";
                worksheet.Cell(1, 2).Value = "Company";
                worksheet.Cell(1, 3).Value = "Street";
                worksheet.Cell(1, 4).Value = "BoxCity";
                worksheet.Cell(1, 5).Value = "Email";
                worksheet.Cell(1, 6).Value = "Versand";
                worksheet.Cell(1, 7).Value = "Status";
                // Add data
                for (int i = 0; i < advertisings.Count; i++)
                {
                    var ad = advertisings[i];
                    worksheet.Cell(i + 2, 1).Value = ad.Branch;
                    worksheet.Cell(i + 2, 2).Value = ad.Company;
                    worksheet.Cell(i + 2, 3).Value = ad.BoxCity;
                    worksheet.Cell(i + 2, 4).Value = ad.Street;
                    worksheet.Cell(i + 2, 5).Value = ad.Email;
                    worksheet.Cell(i + 2, 6).Value = ad.Versand?.ToString("dd.MM.yyyy");
                    worksheet.Cell(i + 2, 7).Value = ad.Status;
                }
                using (var stream = new MemoryStream())
                {
                    workbook.SaveAs(stream);
                    stream.Position = 0;
                    return File(stream.ToArray(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "AdvertisingData.xlsx");
                }
            }
        }

        #region Helpers 

        private async Task PopulateViewBags()
        {
            var textData = await emailTextService.Get();
            ViewBag.EmailTexts = textData.Select(m => new SelectListItem
            {
                Text = m.Title,
                Value = m.Id.ToString()
            }).ToList();

            ViewBag.Branchs = await context.Advertising
                .Select(m => new SelectListItem
                {
                    Text = m.Branch,
                    Value = m.Branch
                }).Distinct().ToListAsync();
        }



        private async Task UpdateEmailStatusBatchRawSql(IEnumerable<dynamic> updates, DateTime timestamp)
        {
            var successIds = updates.Where(u => u.Status == "Success").Select(u => u.Id).ToList();
            var failedIds = updates.Where(u => u.Status == "Failed").Select(u => u.Id).ToList();

            if (successIds.Any())
            {
                var successIdsList = string.Join(",", successIds);
                await context.Database.ExecuteSqlRawAsync(
                    $"UPDATE Advertising SET Status = 'Success', Versand = {{0}} WHERE Id IN ({successIdsList})",
                    timestamp);
            }

            if (failedIds.Any())
            {
                var failedIdsList = string.Join(",", failedIds);
                await context.Database.ExecuteSqlRawAsync(
                    $"UPDATE Advertising SET Status = 'Failed', Versand = {{0}} WHERE Id IN ({failedIdsList})", timestamp);
            }
        }

        // Helper class for tracking email results
        private class EmailResult
        {
            public int Id { get; set; }
            public string Email { get; set; }
            public bool Success { get; set; }
            public string ErrorMessage { get; set; }
        }

        #endregion
    }
}
