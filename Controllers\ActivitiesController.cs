﻿using Admin.TaskDotNet.Dtos;
using AutoMapper;
using Comman.Helper.Extensions;
using Comman.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using System.Text;
using TaskDotNet.Comman.DataAccess;
using TaskDotNet.Helper;
using TaskDotNet.Localization;
using TaskDotNet.Models;

namespace Admin.TaskDotNet.Controllers
{
    [Authorize(Roles = "Admin,Moderator")]
    public class ActivitiesController : Controller
    {

        #region Ctor

        private readonly IMapper mapper;
        private readonly IActivityService activityService;
        private readonly ApplicationDbContext _context;
        private readonly IStringLocalizer<SharedResource> SharedLocalizer;
        public ActivitiesController(IMapper mapper, IActivityService activityService, ApplicationDbContext context, IStringLocalizer<SharedResource> sharedLocalizer)
        {
            this.mapper = mapper;
            this.activityService = activityService;
            _context = context;
            SharedLocalizer = sharedLocalizer;
        }
        #endregion

        #region Get All Activity
        public async Task<IActionResult> GetAllActivities(PaginatedRequest request)
        {

            // Build the query for activities
            var query = _context.Activities.Where(m => m.IsChecked).AsQueryable();

            if (request.SearchTerm != 0)
            {
                query = query.Where(m => m.ActivityType == (ActivityType)request.SearchTerm);
            }

            int totalCount = query.Count();
            request.PageSize = 9;

            IEnumerable<Activity> activities = query.OrderByDescending(a => a.ActivityType == ActivityType.Cleaning ? a.CleaningDate : a.MovingDate)
                    .Skip((request.PageNumber - 1) * request.PageSize)
                    .Take(request.PageSize);

            var partnerActivities = PartnerHelper.GetBooleanProperties(new PartnerActivities());

            ViewBag.PartnerActivities = partnerActivities
                .Where(m => Enum.TryParse<ActivityType>(m.Name, true, out _)) // Filter only valid enum values
                .Select(m => new SelectListItem
                {
                    Value = ((int)Enum.Parse<ActivityType>(m.Name, true)).ToString(), // Case-insensitive parsing
                    Text = m.DisplayName
                });

            return View(new PaginatedListResponse<Activity>(
                data: activities ?? Enumerable.Empty<Activity>(),
                totalCount: totalCount,
                pageNumber: request.PageNumber,
                pageSize: request.PageSize,
                searchTerm: request.SearchTerm
            ));

        }
        #endregion

        #region New Activities
        public async Task<IActionResult> NewActivities()
        {
            // Get activities where IsChecked is false
            var newActivities = await _context.Activities
                .Where(a => !a.IsChecked)
                .OrderByDescending(a => a.ActivityType == ActivityType.Cleaning ? a.CleaningDate : a.MovingDate)
                .ToListAsync();

            return View(newActivities);
        }
        #endregion

        #region Mark Activity as Checked
        [HttpPost]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> MarkAsChecked(int id)
        {
            var activity = await _context.Activities.FindAsync(id);
            if (activity != null)
            {
                activity.IsChecked = true;
                await _context.SaveChangesAsync();
            }
            return RedirectToAction("NewActivities");
        }
        #endregion

        #region Delete Activity
        [HttpPost]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> DeleteActivity(int id)
        {
            var activity = await _context.Activities
                .Include(a => a.InventoryItems)
                .FirstOrDefaultAsync(a => a.Id == id);

            if (activity != null)
            {
                // Remove related inventory items first
                if (activity.InventoryItems != null && activity.InventoryItems.Any())
                {
                    _context.ActivityInventoryItems.RemoveRange(activity.InventoryItems);
                }

                // Remove the activity
                _context.Activities.Remove(activity);
                await _context.SaveChangesAsync();
            }
            return RedirectToAction("NewActivities");
        }
        #endregion

        #region Details
        public async Task<IActionResult> ActivityDetails(int id)
        {
            var data = await _context.Activities.Where(m=>m.Id == id).Include(m=>m.InventoryItems).FirstOrDefaultAsync();
            if (data == null)
            {
                return NotFound();
            }
            string currentLanguage = HttpContext.Features.Get<IRequestCultureFeature>().RequestCulture.Culture.TwoLetterISOLanguageName;

            ViewBag.InventoryHtml = GetInventoryHtml(data.InventoryItems, currentLanguage);

            var model = mapper.Map<ActivityDto>(data);

            return model.ActivityType switch
            {
                ActivityType.Moving => View("MovingActivityDetails", model),
                ActivityType.Cleaning => View("CleaningActivityDetails", model),
                ActivityType.MovingAndCleaning => View("MovingAndCleaningActivityDetails", model),
                ActivityType.PaintingAndGisper => View("PaintingAndGisperActivityDetails", model),
                _ => View("WorkersActivityDetails", model),
            };

        }

        #endregion


        private string GetInventoryHtml(List<ActivityInventoryItem> inventoryData, string lang)
        {
            StringBuilder servicesHtml = new();

            string furnitureHtml = string.Empty;
            if (inventoryData.Any())
            {
                var table = new StringBuilder();
                table.Append($@"
                    <div class='col'>
                        <h4 style='font-siye=20px;font-weight: 600;text-align: center;text-decoration: underline;'>{SharedLocalizer["Inventory list"]}:</h4>
                        <table class='table table-bordered table-responsive table-striped' style='border-collapse: collapse;width:100%;'>
                            <thead style='background-color: #008284; color:white; font-weight: bolder;'>
                                <tr>
                                    <th style='padding: 8px; text-align: left; font-weight: bold;color: white;'>{SharedLocalizer["Space"]}</th>
                                    <th style='padding: 8px; text-align: left; font-weight: bold;color: white;'>{SharedLocalizer["Items"]}</th>
                                    <th style='padding: 8px; text-align: left; font-weight: bold;color: white;'>{SharedLocalizer["Number"]}</th>
                                    <th style='padding: 8px; text-align: left; font-weight: bold;color: white;'>{SharedLocalizer["Total volume"]}</th>
                                </tr>
                            </thead>
                            <tbody>
                ");

                decimal totalVolumes = 0;

                var groupedInventory = inventoryData
                    .Where(item => !string.IsNullOrEmpty(item.Category))
                    .GroupBy(item => item.Category)
                    .ToList();

                foreach (var room in groupedInventory)
                {

                    string? roomName = room.Key;

                    var furnitureList = room.ToList();

                    foreach (var furniture in furnitureList)
                    {
                        string? furnitureName = lang switch
                        {
                            "de" => furniture.GermanName,
                            "en" => furniture.EnglishName,
                            "it" => furniture.ItalianName,
                            "fr" => furniture.FrenchName,
                            _ => furniture.EnglishName
                        };

                        var furnitureQuantity = furniture.Count;
                        double furnitureTotalVolumes = furniture.Total ?? 0;

                        table.Append("<tr>");
                        if (furniture == furnitureList.First())
                        {
                            table.Append($"<td rowspan='{furnitureList.Count}' style='border: 1px solid #ddd; padding: 8px;'>{roomName}</td>");
                        }

                        table.Append($@"
                            <td style='border: 1px solid #ddd; padding: 8px;'>{furnitureName}</td>
                            <td style='border: 1px solid #ddd; padding: 8px;'>{furnitureQuantity} {furniture.Unit}</td>
                            <td style='text-align: center;border: 1px solid #ddd; padding: 8px;'>{furnitureTotalVolumes.ToString("0.00")} m3</td>
                        ");

                        totalVolumes += (decimal)furnitureTotalVolumes;

                        table.Append("</tr>");
                    }

                }

                table.Append($@"
                                <tr><td colspan='4' style='border: 1px solid #ddd; padding: 8px;'></td></tr>
                                <tr class='total-weight-row' style='font-weight: bold;'>
                                    <td colspan='3' style='text-align: right; border: 1px solid #ddd; padding: 8px;'>{@SharedLocalizer["Total volume"]}</td>
                                    <td style='text-align: center; border: 1px solid #ddd; padding: 8px;'>{totalVolumes.ToString("0.00")} m3</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                ");

                furnitureHtml = table.ToString();
            }
            return furnitureHtml;
        }
    }
}
