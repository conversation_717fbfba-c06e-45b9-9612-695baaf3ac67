﻿@page
@using TaskDotNet.Localization
@using Microsoft.Extensions.Localization
@model SettingsModel
@inject IStringLocalizer<SharedResource> SharedLocalizer

@{
    ViewData["Title"] = "Settings";
    Layout = "/Views/Shared/_Layout.cshtml";
}


<!-- Content -->

<div class="container-xxl">
    <div class="container-p-y">
        <partial name="_StatusMessage" for="StatusMessage" />
        <div>
            <div class="card">
                <div class="card-body">
                    <h4 class="card-header">@SharedLocalizer["Account Settings"] </h4>
                    <hr class="my-0" />
                    <!-- Account -->
                    <form method="post" class="mb-3">
                        <div asp-validation-summary="All" class="text-danger"> </div>
                        <div class="row justify-content-center">
                            <div class="mb-3 col-md-4">
                                <label asp-for="Input.Name" class="form-label">@SharedLocalizer["Name"]</label>
                                <input class="form-control" asp-for="Input.Name" />
                                <span asp-validation-for="Input.Name" class="text-danger"></span>
                            </div>
                            <div class="mb-3 col-md-4">
                                <label class="form-label" asp-for="Input.Email">@SharedLocalizer["Email"]</label>
                                <input class="form-control" asp-for="Input.Email" />
                                <span asp-validation-for="Input.Email" class="text-danger"></span>
                            </div>
                            <div class="mb-3 col-md-4">
                                <label class="form-label" asp-for="Input.PhoneNumber">@SharedLocalizer["Phone"]</label>
                                <input class="form-control" asp-for="Input.PhoneNumber" />
                                <span asp-validation-for="Input.PhoneNumber" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="mb-3 d-flex">
                            <input class="btn btn-primary d-grid " value="@SharedLocalizer["Save"]" type="submit" />
                            <a asp-controller="Home" asp-action="Index" class="btn btn-secondary mx-2">@SharedLocalizer["Cancel"]</a>
                        </div>
                    </form>

                    <hr class="mb-3" />

                    <h4 class="card-header">@SharedLocalizer["Change password"]</h4>
                    <hr class="my-0" />
                    <form asp-page="/Account/Manage/Index" method="post" class="mb-3">
                        <div asp-validation-summary="All" class="text-danger"></div>

                        <div class="mb-3 form-password-toggle">
                            <div class="d-flex justify-content-between">
                                <label class="form-label" for="OldPassword">@SharedLocalizer["Old Paassword"]</label>
                                <a href="auth-forgot-password-basic.html">
                                </a>
                            </div>
                            <div class="input-group input-group-merge">
                                <input type="password" name="OldPassword" Id="OldPassword"
                                       class="form-control"
                                       placeholder="&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;"
                                       aria-describedby="password" />
                                <span class="input-group-text cursor-pointer"><i class="bx bx-hide"></i></span>
                            </div>
                        </div>

                        <div class="mb-3 form-password-toggle">
                            <div class="d-flex justify-content-between">
                                <label class="form-label" for="Password">@SharedLocalizer["New Password"]</label>
                                <a href="auth-forgot-password-basic.html">
                                </a>
                            </div>
                            <div class="input-group input-group-merge">
                                <input type="password" name="Password" Id="Password"
                                       class="form-control"
                                       placeholder="&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;"
                                       aria-describedby="password" />
                                <span class="input-group-text cursor-pointer"><i class="bx bx-hide"></i></span>
                            </div>
                        </div>

                        <div class="mb-3 form-password-toggle">
                            <div class="d-flex justify-content-between">
                                <label class="form-label" for="ConfirmPassword">@SharedLocalizer["Confirm New Password"]</label>
                                <a href="auth-forgot-password-basic.html">
                                </a>
                            </div>
                            <div class="input-group input-group-merge">
                                <input type="password" name="ConfirmPassword" Id="ConfirmPassword"
                                       class="form-control"
                                       placeholder="&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;"
                                       aria-describedby="password" />
                                <span class="input-group-text cursor-pointer"><i class="bx bx-hide"></i></span>
                            </div>
                        </div>

                        <div class="mb-3 d-flex">
                            <input class="btn btn-primary d-grid " value="@SharedLocalizer["Save"]" type="submit" />
                            <a asp-controller="Home" asp-action="Index" class="btn btn-secondary mx-2">@SharedLocalizer["Cancel"]</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>



@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
