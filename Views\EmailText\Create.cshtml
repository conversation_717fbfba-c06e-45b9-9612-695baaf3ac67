﻿@model Admin.TaskDotNet.Dtos.EmailTextDto
@{
    ViewData["Title"] = "Create";
    var currentLangeCode = System.Globalization.CultureInfo.CurrentCulture.TwoLetterISOLanguageName;
}

@section Links {
    <style>
        .cke_notifications_area {
            display: none;
        }
    </style>
}

<div class="content-wrapper">
    <!-- Content -->

    <div class="container-fluid flex-grow-1 ">
        <h3 class="fw-bold py-3 mb-4 text-white"><span></span>@SharedLocalizer["Create entry"]</h3>

        <div class="row">
            <div class="col-md-12">

                <div class="card mb-4">
                    <!-- Account -->

                    <hr class="my-0" />
                    <div class="card-body">
                        <form id="myForm" asp-controller="EmailText" asp-action="Create" enctype="multipart/form-data">
                            <div class="row">

                                <div class="mb-3 col-md-12">
                                    <label for="Name" class="form-label">@SharedLocalizer["Title"]</label>
                                    <input class="form-control"
                                           type="text"
                                           asp-for="Title" />
                                </div>

                                <div class="mb-3 col-md-12">
                                    <textarea asp-for="Content" id="editor"></textarea>

                                </div>

                            </div>
                            <div class="mt-2">
                                <input type="submit" class="btn btn-primary me-2" value="@SharedLocalizer["Save"] " />
                                <a asp-controller="EmailText" asp-action="Index" class="btn btn-outline-secondary">@SharedLocalizer["Cancel"] </a>
                            </div>
                        </form>
                    </div>
                    <!-- /Account -->
                </div>

            </div>
        </div>
    </div>
    <!-- / Content -->
</div>
@section Scripts {
    <script src="https://cdn.ckeditor.com/4.22.1/full/ckeditor.js"></script>

    <script>
        $(document).ready(function () {
            // Initialize CKEditor
            CKEDITOR.replace('editor', {
                // CKEditor configuration options
                height: 400,
                language: '@currentLangeCode'

            });
        });
    </script>

}